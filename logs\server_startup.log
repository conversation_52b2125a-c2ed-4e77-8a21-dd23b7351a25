2025-07-11 12:26:07,040 - cache_config - INFO - Redis缓存连接初始化成功
2025-07-11 12:26:07,143 - database - INFO - 数据库操作 - test_connection on system (耗时: 0.022s)
2025-07-11 12:26:07,154 - database - INFO - 人大金仓数据库连接初始化成功
2025-07-11 12:26:07,268 - database - INFO - 数据库连接池初始化成功 - 最小连接数: 5, 最大连接数: 20
2025-07-11 12:26:07,294 - database - INFO - 数据库操作 - test_connection on system (耗时: 0.022s)
2025-07-11 12:26:07,323 - database - INFO - ✅ 人大金仓数据库初始化成功
2025-07-11 12:26:07,327 - app - INFO - 人大金仓数据库连接成功
2025-07-11 12:26:07,343 - app - INFO - 数据库连接池状态: {'status': 'active', 'min_conn': 5, 'max_conn': 20, 'current_conn': 5, 'available_conn': 5, 'used_conn': 0, 'pool_utilization': 0.0}
2025-07-11 12:26:07,368 - app - INFO - Redis缓存连接成功: {'connected_clients': 1, 'used_memory': 28245232, 'used_memory_human': '26.94M', 'keyspace_hits': 128, 'keyspace_misses': 0, 'hit_rate': 100.0, 'total_commands_processed': 178}
2025-07-11 12:26:07,431 - __main__ - INFO - 启动Waitress服务器 0.0.0.0:5000
2025-07-11 12:26:07,485 - waitress - INFO - Serving on http://0.0.0.0:5000
2025-07-11 12:26:13,958 - database - INFO - 数据库操作 - query on users (耗时: 0.013s)
2025-07-11 12:26:14,393 - waitress.queue - WARNING - Task queue depth is 1
2025-07-11 12:26:14,825 - database - INFO - 数据库操作 - query on security_keywords (耗时: 0.004s)
2025-07-11 12:26:14,835 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.013s)
2025-07-11 12:26:14,838 - database - INFO - 数据库操作 - query on security_rules (耗时: 0.008s)
2025-07-11 12:26:14,849 - api - INFO - API调用 - GET /api/security/config (状态码: 200)
2025-07-11 12:26:22,174 - waitress.queue - WARNING - Task queue depth is 1
2025-07-11 12:26:22,175 - waitress.queue - WARNING - Task queue depth is 2
2025-07-11 12:26:22,486 - database - INFO - 数据库操作 - query on security_rules (耗时: 0.001s)
2025-07-11 12:26:22,489 - api - INFO - API调用 - GET /api/security/config (状态码: 200)
2025-07-11 12:32:20,798 - cache_config - INFO - Redis缓存连接初始化成功
2025-07-11 12:32:20,879 - database - INFO - 数据库操作 - test_connection on system (耗时: 0.028s)
2025-07-11 12:32:20,880 - database - INFO - 人大金仓数据库连接初始化成功
2025-07-11 12:32:20,975 - database - INFO - 数据库连接池初始化成功 - 最小连接数: 5, 最大连接数: 20
2025-07-11 12:32:20,996 - database - INFO - 数据库操作 - test_connection on system (耗时: 0.020s)
2025-07-11 12:32:20,998 - database - INFO - ✅ 人大金仓数据库初始化成功
2025-07-11 12:32:20,999 - app - INFO - 人大金仓数据库连接成功
2025-07-11 12:32:21,000 - app - INFO - 数据库连接池状态: {'status': 'active', 'min_conn': 5, 'max_conn': 20, 'current_conn': 5, 'available_conn': 5, 'used_conn': 0, 'pool_utilization': 0.0}
2025-07-11 12:32:21,002 - app - INFO - Redis缓存连接成功: {'connected_clients': 1, 'used_memory': 28262784, 'used_memory_human': '26.95M', 'keyspace_hits': 131, 'keyspace_misses': 3, 'hit_rate': 97.76, 'total_commands_processed': 206}
2025-07-11 12:32:21,030 - __main__ - INFO - 启动Waitress服务器 0.0.0.0:5000
2025-07-11 12:32:21,032 - waitress - INFO - Serving on http://0.0.0.0:5000
2025-07-11 12:33:43,918 - cache_config - INFO - Redis缓存连接初始化成功
2025-07-11 12:33:43,992 - database - INFO - 数据库操作 - test_connection on system (耗时: 0.021s)
2025-07-11 12:33:43,992 - database - INFO - 人大金仓数据库连接初始化成功
2025-07-11 12:33:44,082 - database - INFO - 数据库连接池初始化成功 - 最小连接数: 5, 最大连接数: 20
2025-07-11 12:33:44,110 - database - INFO - 数据库操作 - test_connection on system (耗时: 0.027s)
2025-07-11 12:33:44,112 - database - INFO - ✅ 人大金仓数据库初始化成功
2025-07-11 12:33:44,113 - app - INFO - 人大金仓数据库连接成功
2025-07-11 12:33:44,114 - app - INFO - 数据库连接池状态: {'status': 'active', 'min_conn': 5, 'max_conn': 20, 'current_conn': 5, 'available_conn': 5, 'used_conn': 0, 'pool_utilization': 0.0}
2025-07-11 12:33:44,116 - app - INFO - Redis缓存连接成功: {'connected_clients': 1, 'used_memory': 28262784, 'used_memory_human': '26.95M', 'keyspace_hits': 131, 'keyspace_misses': 3, 'hit_rate': 97.76, 'total_commands_processed': 213}
2025-07-11 12:33:44,130 - __main__ - INFO - 启动Waitress服务器 0.0.0.0:5000
2025-07-11 12:33:44,134 - waitress - INFO - Serving on http://0.0.0.0:5000
2025-07-11 12:34:59,701 - database - INFO - 数据库操作 - query on security_rules (耗时: 0.004s)
2025-07-11 12:34:59,702 - api - INFO - API调用 - GET /api/security/config (状态码: 200)
2025-07-11 12:34:59,710 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.012s)
2025-07-11 12:35:00,528 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.003s)
2025-07-11 12:35:03,502 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.838s)
2025-07-11 12:35:40,936 - database - INFO - 数据库操作 - update on users (耗时: 0.004s)
2025-07-11 12:35:40,939 - database - INFO - 数据库操作 - get_user on users (耗时: 0.008s)
2025-07-11 12:35:40,943 - database - INFO - 数据库操作 - insert on chat_sessions (耗时: 0.002s)
2025-07-11 12:35:40,946 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.002s)
2025-07-11 12:35:40,949 - database - INFO - 数据库操作 - insert on chat_messages (耗时: 0.003s)
2025-07-11 12:35:40,953 - database - INFO - 数据库操作 - update on chat_sessions (耗时: 0.002s)
2025-07-11 12:35:40,957 - database - INFO - 数据库操作 - query on users (耗时: 0.001s)
2025-07-11 12:35:40,960 - database - INFO - 数据库操作 - update on users (耗时: 0.002s)
2025-07-11 12:35:40,961 - database - INFO - 数据库操作 - get_user on users (耗时: 0.006s)
2025-07-11 12:35:40,966 - database - INFO - 数据库操作 - insert on chat_sessions (耗时: 0.003s)
2025-07-11 12:35:40,969 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.001s)
2025-07-11 12:35:40,991 - database - INFO - 数据库操作 - insert on chat_messages (耗时: 0.021s)
2025-07-11 12:35:40,993 - database - INFO - 数据库操作 - update on chat_sessions (耗时: 0.001s)
2025-07-11 12:35:41,606 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.003s)
2025-07-11 12:36:13,183 - database - INFO - 数据库操作 - query on users (耗时: 0.001s)
2025-07-11 12:36:13,187 - database - INFO - 数据库操作 - update on users (耗时: 0.002s)
2025-07-11 12:36:13,189 - database - INFO - 数据库操作 - get_user on users (耗时: 0.007s)
2025-07-11 12:36:13,192 - database - INFO - 数据库操作 - insert on chat_sessions (耗时: 0.002s)
2025-07-11 12:36:13,195 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.001s)
2025-07-11 12:36:13,198 - database - INFO - 数据库操作 - insert on chat_messages (耗时: 0.002s)
2025-07-11 12:36:13,200 - database - INFO - 数据库操作 - update on chat_sessions (耗时: 0.001s)
2025-07-11 12:36:13,204 - database - INFO - 数据库操作 - query on users (耗时: 0.001s)
2025-07-11 12:36:13,207 - database - INFO - 数据库操作 - update on users (耗时: 0.002s)
2025-07-11 12:36:13,209 - database - INFO - 数据库操作 - get_user on users (耗时: 0.007s)
2025-07-11 12:36:13,211 - database - INFO - 数据库操作 - insert on chat_sessions (耗时: 0.002s)
2025-07-11 12:36:13,213 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.001s)
2025-07-11 12:36:13,217 - database - INFO - 数据库操作 - insert on chat_messages (耗时: 0.002s)
2025-07-11 12:36:13,220 - database - INFO - 数据库操作 - update on chat_sessions (耗时: 0.002s)
2025-07-11 12:36:13,536 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.006s)
2025-07-11 12:37:00,799 - database - INFO - 数据库操作 - query on users (耗时: 0.001s)
2025-07-11 12:37:00,802 - database - INFO - 数据库操作 - update on users (耗时: 0.001s)
2025-07-11 12:37:00,803 - database - INFO - 数据库操作 - get_user on users (耗时: 0.006s)
2025-07-11 12:37:00,805 - database - INFO - 数据库操作 - insert on chat_sessions (耗时: 0.001s)
2025-07-11 12:37:00,809 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.002s)
2025-07-11 12:37:00,812 - database - INFO - 数据库操作 - insert on chat_messages (耗时: 0.002s)
2025-07-11 12:37:00,815 - database - INFO - 数据库操作 - update on chat_sessions (耗时: 0.002s)
2025-07-11 12:37:00,817 - database - INFO - 数据库操作 - query on users (耗时: 0.001s)
2025-07-11 12:37:00,822 - database - INFO - 数据库操作 - update on users (耗时: 0.003s)
2025-07-11 12:37:00,824 - database - INFO - 数据库操作 - get_user on users (耗时: 0.008s)
2025-07-11 12:37:00,827 - database - INFO - 数据库操作 - insert on chat_sessions (耗时: 0.001s)
2025-07-11 12:37:00,831 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.001s)
2025-07-11 12:37:01,171 - database - INFO - 数据库操作 - insert on chat_messages (耗时: 0.320s)
2025-07-11 12:37:01,174 - database - INFO - 数据库操作 - update on chat_sessions (耗时: 0.002s)
2025-07-11 12:37:06,618 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.004s)
2025-07-11 12:42:26,844 - cache_config - INFO - Redis缓存连接初始化成功
2025-07-11 12:42:26,922 - database - INFO - 数据库操作 - test_connection on system (耗时: 0.021s)
2025-07-11 12:42:26,923 - database - INFO - 人大金仓数据库连接初始化成功
2025-07-11 12:42:27,011 - database - INFO - 数据库连接池初始化成功 - 最小连接数: 5, 最大连接数: 20
2025-07-11 12:42:27,028 - database - INFO - 数据库操作 - test_connection on system (耗时: 0.016s)
2025-07-11 12:42:27,028 - database - INFO - ✅ 人大金仓数据库初始化成功
2025-07-11 12:42:27,029 - app - INFO - 人大金仓数据库连接成功
2025-07-11 12:42:27,030 - app - INFO - 数据库连接池状态: {'status': 'active', 'min_conn': 5, 'max_conn': 20, 'current_conn': 5, 'available_conn': 5, 'used_conn': 0, 'pool_utilization': 0.0}
2025-07-11 12:42:27,031 - app - INFO - Redis缓存连接成功: {'connected_clients': 3, 'used_memory': 40887136, 'used_memory_human': '38.99M', 'keyspace_hits': 138, 'keyspace_misses': 14, 'hit_rate': 90.79, 'total_commands_processed': 335}
2025-07-11 12:42:27,043 - __main__ - INFO - 启动Waitress服务器 0.0.0.0:5000
2025-07-11 12:42:27,047 - waitress - INFO - Serving on http://0.0.0.0:5000
2025-07-11 12:46:34,257 - cache_config - INFO - Redis缓存连接初始化成功
2025-07-11 12:46:34,331 - database - INFO - 数据库操作 - test_connection on system (耗时: 0.029s)
2025-07-11 12:46:34,333 - database - INFO - 人大金仓数据库连接初始化成功
2025-07-11 12:46:34,429 - database - INFO - 数据库连接池初始化成功 - 最小连接数: 5, 最大连接数: 20
2025-07-11 12:46:34,447 - database - INFO - 数据库操作 - test_connection on system (耗时: 0.014s)
2025-07-11 12:46:34,448 - database - INFO - ✅ 人大金仓数据库初始化成功
2025-07-11 12:46:34,450 - app - INFO - 人大金仓数据库连接成功
2025-07-11 12:46:34,451 - app - INFO - 数据库连接池状态: {'status': 'active', 'min_conn': 5, 'max_conn': 20, 'current_conn': 5, 'available_conn': 5, 'used_conn': 0, 'pool_utilization': 0.0}
2025-07-11 12:46:34,453 - app - INFO - Redis缓存连接成功: {'connected_clients': 1, 'used_memory': 28262320, 'used_memory_human': '26.95M', 'keyspace_hits': 138, 'keyspace_misses': 14, 'hit_rate': 90.79, 'total_commands_processed': 342}
2025-07-11 12:46:34,463 - __main__ - INFO - 启动Waitress服务器 0.0.0.0:5000
2025-07-11 12:46:34,469 - waitress - INFO - Serving on http://0.0.0.0:5000
2025-07-11 12:47:35,232 - database - INFO - 数据库操作 - query on users (耗时: 0.008s)
2025-07-11 12:47:35,979 - database - INFO - 数据库操作 - query on security_rules (耗时: 0.007s)
2025-07-11 12:47:35,982 - api - INFO - API调用 - GET /api/security/config (状态码: 200)
2025-07-11 12:47:36,008 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.032s)
2025-07-11 12:47:37,312 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.235s)
2025-07-11 12:47:38,228 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.001s)
2025-07-11 12:47:39,387 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.401s)
2025-07-11 12:47:40,619 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.001s)
2025-07-11 12:47:44,561 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.487s)
2025-07-11 12:47:45,539 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.001s)
2025-07-11 12:48:45,176 - database - INFO - 数据库操作 - update on users (耗时: 0.004s)
2025-07-11 12:48:45,180 - database - INFO - 数据库操作 - get_user on users (耗时: 0.009s)
2025-07-11 12:48:45,187 - database - INFO - 数据库操作 - insert on chat_sessions (耗时: 0.003s)
2025-07-11 12:48:45,191 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.002s)
2025-07-11 12:48:45,198 - database - INFO - 数据库操作 - insert on chat_messages (耗时: 0.002s)
2025-07-11 12:48:45,200 - database - INFO - 数据库操作 - update on chat_sessions (耗时: 0.002s)
2025-07-11 12:48:45,212 - database - INFO - 数据库操作 - query on users (耗时: 0.001s)
2025-07-11 12:48:45,216 - database - INFO - 数据库操作 - update on users (耗时: 0.002s)
2025-07-11 12:48:45,223 - database - INFO - 数据库操作 - get_user on users (耗时: 0.012s)
2025-07-11 12:48:45,225 - database - INFO - 数据库操作 - insert on chat_sessions (耗时: 0.002s)
2025-07-11 12:48:45,233 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.001s)
2025-07-11 12:48:46,006 - database - INFO - 数据库操作 - insert on chat_messages (耗时: 0.752s)
2025-07-11 12:48:46,010 - database - INFO - 数据库操作 - update on chat_sessions (耗时: 0.002s)
2025-07-11 12:48:52,403 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.005s)
2025-07-11 12:49:15,094 - database - INFO - 数据库操作 - insert on chat_sessions (耗时: 0.001s)
2025-07-11 12:49:46,783 - database - INFO - 数据库操作 - query on users (耗时: 0.002s)
2025-07-11 12:49:46,790 - database - INFO - 数据库操作 - update on users (耗时: 0.003s)
2025-07-11 12:49:46,795 - database - INFO - 数据库操作 - get_user on users (耗时: 0.015s)
2025-07-11 12:49:46,802 - database - INFO - 数据库操作 - insert on chat_sessions (耗时: 0.003s)
2025-07-11 12:49:46,808 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.001s)
2025-07-11 12:49:46,812 - database - INFO - 数据库操作 - insert on chat_messages (耗时: 0.003s)
2025-07-11 12:49:46,817 - database - INFO - 数据库操作 - update on chat_sessions (耗时: 0.002s)
2025-07-11 12:49:46,820 - database - INFO - 数据库操作 - query on users (耗时: 0.001s)
2025-07-11 12:49:46,827 - database - INFO - 数据库操作 - update on users (耗时: 0.002s)
2025-07-11 12:49:46,829 - database - INFO - 数据库操作 - get_user on users (耗时: 0.010s)
2025-07-11 12:49:46,832 - database - INFO - 数据库操作 - insert on chat_sessions (耗时: 0.002s)
2025-07-11 12:49:46,843 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.004s)
2025-07-11 12:49:46,851 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.012s)
2025-07-11 12:49:46,857 - database - INFO - 数据库操作 - insert on chat_messages (耗时: 0.005s)
2025-07-11 12:49:46,879 - database - INFO - 数据库操作 - update on chat_sessions (耗时: 0.004s)
2025-07-11 12:49:47,206 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.010s)
2025-07-11 12:50:18,025 - database - INFO - 数据库操作 - query on users (耗时: 0.003s)
2025-07-11 12:50:18,037 - database - INFO - 数据库操作 - update on users (耗时: 0.004s)
2025-07-11 12:50:18,039 - database - INFO - 数据库操作 - get_user on users (耗时: 0.018s)
2025-07-11 12:50:18,049 - database - INFO - 数据库操作 - insert on chat_sessions (耗时: 0.003s)
2025-07-11 12:50:18,065 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.003s)
2025-07-11 12:50:18,069 - database - INFO - 数据库操作 - insert on chat_messages (耗时: 0.003s)
2025-07-11 12:50:18,073 - database - INFO - 数据库操作 - update on chat_sessions (耗时: 0.003s)
2025-07-11 12:50:18,082 - database - INFO - 数据库操作 - query on users (耗时: 0.002s)
2025-07-11 12:50:18,088 - database - INFO - 数据库操作 - update on users (耗时: 0.002s)
2025-07-11 12:50:18,093 - database - INFO - 数据库操作 - get_user on users (耗时: 0.013s)
2025-07-11 12:50:18,103 - database - INFO - 数据库操作 - insert on chat_sessions (耗时: 0.002s)
2025-07-11 12:50:18,107 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.002s)
2025-07-11 12:50:18,115 - database - INFO - 数据库操作 - insert on chat_messages (耗时: 0.003s)
2025-07-11 12:50:18,120 - database - INFO - 数据库操作 - update on chat_sessions (耗时: 0.003s)
2025-07-11 12:50:18,749 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.008s)
2025-07-11 12:50:58,432 - database - INFO - 数据库操作 - query on users (耗时: 0.001s)
2025-07-11 12:50:58,436 - database - INFO - 数据库操作 - update on users (耗时: 0.002s)
2025-07-11 12:50:58,440 - database - INFO - 数据库操作 - get_user on users (耗时: 0.010s)
2025-07-11 12:50:58,444 - database - INFO - 数据库操作 - insert on chat_sessions (耗时: 0.002s)
2025-07-11 12:50:58,451 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.001s)
2025-07-11 12:50:58,454 - database - INFO - 数据库操作 - insert on chat_messages (耗时: 0.002s)
2025-07-11 12:50:58,466 - database - INFO - 数据库操作 - update on chat_sessions (耗时: 0.003s)
2025-07-11 12:50:58,469 - database - INFO - 数据库操作 - query on users (耗时: 0.001s)
2025-07-11 12:50:58,476 - database - INFO - 数据库操作 - update on users (耗时: 0.002s)
2025-07-11 12:50:58,478 - database - INFO - 数据库操作 - get_user on users (耗时: 0.010s)
2025-07-11 12:50:58,487 - database - INFO - 数据库操作 - insert on chat_sessions (耗时: 0.002s)
2025-07-11 12:50:58,490 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.002s)
2025-07-11 12:50:59,266 - database - INFO - 数据库操作 - insert on chat_messages (耗时: 0.751s)
2025-07-11 12:50:59,270 - database - INFO - 数据库操作 - update on chat_sessions (耗时: 0.002s)
2025-07-11 12:51:03,507 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.003s)
2025-07-11 12:51:53,258 - database - INFO - 数据库操作 - query on users (耗时: 0.001s)
2025-07-11 12:51:53,263 - database - INFO - 数据库操作 - update on users (耗时: 0.001s)
2025-07-11 12:51:53,267 - database - INFO - 数据库操作 - get_user on users (耗时: 0.011s)
2025-07-11 12:51:53,269 - database - INFO - 数据库操作 - insert on chat_sessions (耗时: 0.001s)
2025-07-11 12:51:53,272 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.001s)
2025-07-11 12:51:53,280 - database - INFO - 数据库操作 - insert on chat_messages (耗时: 0.002s)
2025-07-11 12:51:53,283 - database - INFO - 数据库操作 - update on chat_sessions (耗时: 0.002s)
2025-07-11 12:51:53,287 - database - INFO - 数据库操作 - query on users (耗时: 0.001s)
2025-07-11 12:51:53,296 - database - INFO - 数据库操作 - update on users (耗时: 0.001s)
2025-07-11 12:51:53,297 - database - INFO - 数据库操作 - get_user on users (耗时: 0.012s)
2025-07-11 12:51:53,300 - database - INFO - 数据库操作 - insert on chat_sessions (耗时: 0.002s)
2025-07-11 12:51:53,303 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.001s)
2025-07-11 12:51:53,640 - database - INFO - 数据库操作 - insert on chat_messages (耗时: 0.313s)
2025-07-11 12:51:53,645 - database - INFO - 数据库操作 - update on chat_sessions (耗时: 0.002s)
2025-07-11 12:51:59,135 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.009s)
2025-07-11 12:53:48,297 - database - INFO - 数据库操作 - query on users (耗时: 0.002s)
2025-07-11 12:53:48,303 - database - INFO - 数据库操作 - update on users (耗时: 0.001s)
2025-07-11 12:53:48,307 - database - INFO - 数据库操作 - get_user on users (耗时: 0.012s)
2025-07-11 12:53:48,309 - database - INFO - 数据库操作 - insert on chat_sessions (耗时: 0.001s)
2025-07-11 12:53:48,311 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.001s)
2025-07-11 12:53:48,316 - database - INFO - 数据库操作 - insert on chat_messages (耗时: 0.003s)
2025-07-11 12:53:48,321 - database - INFO - 数据库操作 - update on chat_sessions (耗时: 0.001s)
2025-07-11 12:53:48,324 - database - INFO - 数据库操作 - query on users (耗时: 0.001s)
2025-07-11 12:53:48,326 - database - INFO - 数据库操作 - update on users (耗时: 0.001s)
2025-07-11 12:53:48,328 - database - INFO - 数据库操作 - get_user on users (耗时: 0.005s)
2025-07-11 12:53:48,337 - database - INFO - 数据库操作 - insert on chat_sessions (耗时: 0.001s)
2025-07-11 12:53:48,340 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.001s)
2025-07-11 12:53:48,868 - database - INFO - 数据库操作 - insert on chat_messages (耗时: 0.505s)
2025-07-11 12:53:48,872 - database - INFO - 数据库操作 - update on chat_sessions (耗时: 0.002s)
2025-07-11 12:53:54,540 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.003s)
2025-07-11 13:49:52,774 - cache_config - INFO - Redis缓存连接初始化成功
2025-07-11 13:49:52,839 - database - INFO - 数据库操作 - test_connection on system (耗时: 0.019s)
2025-07-11 13:49:52,839 - database - INFO - 人大金仓数据库连接初始化成功
2025-07-11 13:49:52,926 - database - INFO - 数据库连接池初始化成功 - 最小连接数: 5, 最大连接数: 20
2025-07-11 13:49:52,943 - database - INFO - 数据库操作 - test_connection on system (耗时: 0.015s)
2025-07-11 13:49:52,943 - database - INFO - ✅ 人大金仓数据库初始化成功
2025-07-11 13:49:52,944 - app - INFO - 人大金仓数据库连接成功
2025-07-11 13:49:52,945 - app - INFO - 数据库连接池状态: {'status': 'active', 'min_conn': 5, 'max_conn': 20, 'current_conn': 5, 'available_conn': 5, 'used_conn': 0, 'pool_utilization': 0.0}
2025-07-11 13:49:52,947 - app - INFO - Redis缓存连接成功: {'connected_clients': 1, 'used_memory': 28246048, 'used_memory_human': '26.94M', 'keyspace_hits': 148, 'keyspace_misses': 40, 'hit_rate': 78.72, 'total_commands_processed': 590}
2025-07-11 13:49:52,957 - __main__ - INFO - 启动Waitress服务器 0.0.0.0:5000
2025-07-11 13:49:52,960 - waitress - INFO - Serving on http://0.0.0.0:5000
2025-07-11 13:50:03,548 - database - INFO - 数据库操作 - query on users (耗时: 0.007s)
2025-07-11 13:50:03,658 - waitress.queue - WARNING - Task queue depth is 1
2025-07-11 13:50:03,685 - waitress.queue - WARNING - Task queue depth is 2
2025-07-11 13:50:04,204 - waitress.queue - WARNING - Task queue depth is 1
2025-07-11 13:50:04,267 - database - INFO - 数据库操作 - query on chat_messages (耗时: 0.035s)
2025-07-11 13:50:04,268 - database - INFO - 数据库操作 - query on security_keywords (耗时: 0.053s)
2025-07-11 13:50:04,387 - database - INFO - 数据库操作 - query on security_rules (耗时: 0.003s)
2025-07-11 13:50:04,404 - api - INFO - API调用 - GET /api/security/config (状态码: 200)
