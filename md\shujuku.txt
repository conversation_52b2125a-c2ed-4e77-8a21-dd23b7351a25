# 昆仑问策聊天系统环境配置模板
# 复制此文件为 .env 并填入实际配置

# 人大金仓数据库配置
KINGBASE_HOST=***************
KINGBASE_PORT=54321
KINGBASE_USER=system
KINGBASE_PASSWORD=abcd1234
KINGBASE_DATABASE=cnpcklwc
KINGBASE_SCHEMA=cnpcklwc

# 是否使用数据库存储（true/false）
USE_DATABASE=true

# 数据库连接池配置
DB_POOL_MIN_CONN=5
DB_POOL_MAX_CONN=20

# Redis缓存配置      python -m pip install -r requirements.txt
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# OpenAI API配置（如果需要）
OPENAI_API_KEY=your_openai_api_key

# Flask应用配置
FLASK_SECRET_KEY=your-super-secret-key-change-this
FLASK_DEBUG=true

# 应用服务器配置
APP_HOST=0.0.0.0
APP_PORT=443

# 安全配置
JWT_SECRET=your-jwt-secret-key-change-this
JWT_EXPIRATION=3600
ADMIN_TOKEN_EXPIRATION=1800
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900
ADMIN_IP_WHITELIST=127.0.0.1,::1,***********/24
ENABLE_2FA=false

# 管理员账户配置 (格式: username:password,username2:password2)
ADMIN_USERS=admin:SecureAdmin123!,system:SystemPass456!