:root {
    --primary-color: #6e48aa;
    --secondary-color: #ff6a00;
    --dark-color: #1a1a2e;
    --light-color: #f8f9fa;
    --gray-color: #e9ecef;
    --text-color: #333;
    --text-light: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --border-radius: 8px;
    --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 80px;
}
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

body {
    background-color: #f5f7fa;
    color: var(--text-color);
    display: flex;
    min-height: 100vh;
    overflow-x: hidden;
}

/* 侧边栏样式 */
.sidebar {
    width: var(--sidebar-width);
    background: linear-gradient(135deg, #1a1a2e 0%, #1a1a2e 100%);
    height: 100vh;
    max-height: 100vh; /* 确保不会超出视窗高度 */
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
    position: relative;
    z-index: 100;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    /*overflow: hidden; 防止内容溢出到侧边栏外 */
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar-toggle {
    position: absolute;
    right: -12px;
    top: 20px;
    width: 24px;
    height: 24px;
    background-color: white;
    border: 1px solid var(--gray-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 101;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.sidebar-toggle:hover {
    background-color: var(--primary-color);
    color: white;
}

.sidebar-header {
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-icon {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 14px;
}

.logo-text {
    font-size: 1.5rem;
    color: white;
    font-weight: 600;
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .logo-text {
    opacity: 0;
    pointer-events: none;
}

.new-chat-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s;
    margin: 15px;
    justify-content: center;
}

.sidebar.collapsed .sidebar-header {
    padding: 15px;
    margin-top: -40px;
}

.sidebar.collapsed .new-chat-btn span {
    display: none;
}

/* 历史记录容器优化 */
.history-container {
    flex: 1;
    overflow: auto;
    padding: 0 0 20px;
    transition: all 0.3s ease;
    max-height: calc(100vh - 200px); /* 限制最大高度，为头部和底部留出空间 */
    min-height: 0; /* 防止flex项目的默认最小高度问题 */
}

.sidebar.collapsed .history-container {
    overflow-y: auto; /* 保持滚动，而不是visible */
    max-height: calc(100vh - 120px); /* 收缩状态下调整高度 */
}

.history-section {
    padding: 12px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.history-section:last-child {
    border-bottom: none;
}

.section-title {
    padding: 8px 20px;
    font-size: 14px;
    color: #c5c5c5;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .section-title {
    opacity: 0;
    pointer-events: none;
    height: 0;
    padding: 0;
    margin: 0;
}

.history-list {
    padding: 4px 0;
}

.history-item {
    padding: 10px 20px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 10px;
    position: relative;
    color: rgba(255, 255, 255, 0.7);
}

.sidebar.collapsed .history-item {
    padding: 10px;
    justify-content: center;
}

.history-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}

.history-item.active {
    background-color: #833f46;
    border-left: 3px solid var(--primary-color);
    color: white;
}

.sidebar.collapsed .history-item.active {
    border-left: none;
    border-radius: 8px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(255, 107, 107, 0.4), rgba(255, 107, 107, 0.25));
    box-shadow:
        0 0 15px rgba(255, 107, 107, 0.5),
        0 0 30px rgba(255, 107, 107, 0.2),
        inset 0 0 15px rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 107, 107, 0.4);
}

.history-item .icon {
    min-width: 24px;
    text-align: center;
    font-size: 16px;
    color: #c5c5c5;
}

.history-item.today .icon {
    color: #40f76b;
}

.history-item.yesterday .icon {
    color: #70b1fc;
}

.history-item.week .icon {
    color: #b186f7;
}

.history-item.month .icon {
    color: #fc9f61;
}

.history-item.older .icon {
    color: #bdbfc2;
}

.history-item .title {
    flex: 1;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .history-item .title {
    display: none;
}

.sidebar.collapsed .history-item {
    padding: 8px;
    justify-content: center;
    width: auto;
    height: 40px;
    margin: 4px 8px;
    border-radius: 8px;
    opacity: 1;
    pointer-events: auto;
    overflow: visible;
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
}

.sidebar.collapsed .history-item:hover {
    background: linear-gradient(135deg, rgba(255, 107, 107, 0.3), rgba(255, 107, 107, 0.2));
    box-shadow:
        0 0 20px rgba(255, 107, 107, 0.4),
        0 0 40px rgba(255, 107, 107, 0.2),
        inset 0 0 20px rgba(255, 255, 255, 0.1);
    transform: translateX(3px) scale(1.05);
    border: 1px solid rgba(255, 107, 107, 0.3);
}

.history-item .time {
    font-size: 12px;
    color: #c5c5c5;
    white-space: nowrap;
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .history-item .time {
    display: none;
}

/* 折叠状态下的图标样式优化 */
.sidebar.collapsed .history-item .icon {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.4);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    filter: brightness(0.6) saturate(0.5);
}

.sidebar.collapsed .history-item:hover .icon {
    color: #ffffff;
    transform: scale(1.4);
    filter: brightness(2) saturate(1.5) drop-shadow(0 0 15px rgba(255, 107, 107, 0.8)) drop-shadow(0 0 25px rgba(255, 107, 107, 0.4));
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.8), 0 0 30px rgba(255, 107, 107, 0.6);
    animation: iconPulse 1.5s ease-in-out infinite;
}

.sidebar.collapsed .history-item.active .icon {
    color: var(--primary-color);
    filter: brightness(1.3) drop-shadow(0 0 10px rgba(255, 107, 107, 0.5));
    transform: scale(1.1);
}

/* 图标脉动动画 */
@keyframes iconPulse {
    0%, 100% {
        filter: brightness(2) saturate(1.5) drop-shadow(0 0 15px rgba(255, 107, 107, 0.8)) drop-shadow(0 0 25px rgba(255, 107, 107, 0.4));
    }
    50% {
        filter: brightness(2.5) saturate(2) drop-shadow(0 0 20px rgba(255, 107, 107, 1)) drop-shadow(0 0 35px rgba(255, 107, 107, 0.6));
        transform: scale(1.5);
    }
}

/* 折叠状态下隐藏操作按钮 */
.sidebar.collapsed .history-item .history-item-actions {
    display: none;
}

.history-item-tooltip {
    position: absolute;
    left: calc(100% + 15px);
    top: 50%;
    transform: translateY(-50%);
    background-color: white;
    color: var(--text-color);
    padding: 8px 12px;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    white-space: nowrap;
    display: none;
    font-size: 0.9rem;
    pointer-events: none;
}

.sidebar.collapsed .history-item:hover .history-item-tooltip {
    display: block;
}

.history-item-tooltip::before {
    content: '';
    position: absolute;
    left: -5px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
    border-right: 5px solid white;
}

/* 操作按钮样式 */
.history-item-actions {
    display: flex;
    align-items: center;
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(30, 30, 50, 0.8);
    border-radius: 15px;
    padding: 2px 5px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.history-item:hover .history-item-actions {
    opacity: 1;
}

.action-btn {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s;
}

.action-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
}

.action-btn-kb {
    background: none;
    border: none;
    color: var(--gray);
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
}

.action-btn-kb:hover {
    color: var(--primary);
}

/* 操作菜单 */
.actions-dropdown {
    position: absolute;
    right: 0;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    z-index: 10;
    min-width: 120px;
    overflow: hidden;
    display: none;
}

.actions-dropdown.active {
    display: block;
}

.dropdown-item {
    padding: 8px 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-color);
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

.dropdown-item.delete {
    color: var(--danger-color);
}

/* 重命名输入框 */
.rename-input {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    color: white;
    font-size: 14px;
    font-weight: 500;
    padding: 6px 10px;
    width: 100%;
    outline: none;
    transition: all 0.2s ease;
    box-shadow: 0 0 0 0 rgba(110, 72, 170, 0.3);
}

.rename-input:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(110, 72, 170, 0.3);
}

.rename-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.rename-input::selection {
    background-color: var(--primary-color);
    color: white;
}

/* 重命名容器 */
.history-item.renaming {
    background-color: rgba(255, 255, 255, 0.05);
    box-shadow: 0 0 0 1px rgba(110, 72, 170, 0.3);
    border-radius: 6px;
}

/* 重命名提示 */
.rename-hint {
    position: absolute;
    bottom: -25px;
    left: 0;
    right: 0;
    font-size: 11px;
    color: rgba(255, 255, 255, 0.6);
    text-align: center;
    opacity: 0;
    animation: fadeInUp 0.3s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(5px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeOutDown {
    from {
        opacity: 1;
        transform: translateY(0);
    }

    to {
        opacity: 0;
        transform: translateY(5px);
    }
}

/* 加载状态 */
.rename-loading {
    display: inline-block;
    width: 12px;
    height: 12px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
    margin-left: 8px;
    vertical-align: middle;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/*个人信息*/
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    position: relative;
}

.sidebar.collapsed .user-info {
    justify-content: center;
    gap: 0px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    flex-shrink: 0;
    font-size: 18px;
}

.user-details {
    flex: 1;
    overflow: hidden;
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .user-details {
    opacity: 0;
    pointer-events: none;
    width: 0;
    height: 0;
    overflow: hidden;
}

.user-name {
    font-weight: 500;
    color: white;
    font-size: 1rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-email {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.8rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-dropdown-icon {
    color: rgba(255, 255, 255, 0.7);
    transition: transform 0.3s ease;
}

.sidebar.collapsed .user-dropdown-icon {
    opacity: 0;
    pointer-events: none;
    width: 0;
    height: 0;
    overflow: hidden;
}

.user-menu {
    position: absolute;
    bottom: 100%;
    left: 0;
    right: 0;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    z-index: 200;
    overflow: hidden;
    display: none;
    margin-bottom: 10px;
}

.user-menu.active {
    display: block;
}

.user-menu-item {
    padding: 12px 15px;
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--text-color);
    cursor: pointer;
    transition: all 0.2s;
    border-bottom: 1px solid var(--gray-color);
}

.user-menu-item:last-child {
    border-bottom: none;
}

.user-menu-item:hover {
    background-color: #f8f9fa;
}

.user-menu-item i {
    width: 20px;
    text-align: center;
}

.user-menu-item.logout {
    color: var(--danger-color);
}

/* 联系我们弹窗 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.contact-modal {
    background: white;
    border-radius: var(--border-radius);
    width: 90%;
    max-width: 500px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    transform: translateY(-20px);
    transition: transform 0.3s ease;
}

.modal-overlay.active .contact-modal {
    transform: translateY(0);
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 20px;
    position: relative;
}

.modal-title {
    font-size: 1.5rem;
    margin: 0;
}

.close-modal {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
}

.modal-body {
    padding: 20px;
}

.contact-methods {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border-radius: var(--border-radius);
    border: 1px solid var(--gray-color);
    transition: all 0.2s;
}

.contact-item:hover {
    background-color: #f8f9fa;
    border-color: var(--primary-color);
}

.contact-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, rgba(110, 72, 170, 0.1), rgba(157, 80, 187, 0.1));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 18px;
}

.contact-details {
    flex: 1;
}

.contact-type {
    font-weight: 600;
    margin-bottom: 3px;
    color: var(--text-color);
}

.contact-info {
    color: var(--text-light);
    font-size: 0.9rem;
}

.modal-footer {
    padding: 15px 20px;
    background-color: #f8f9fa;
    text-align: center;
    border-top: 1px solid var(--gray-color);
}

.feedback-text {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-bottom: 10px;
}

/* 主内容区样式 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100vh;
    transition: all 0.3s ease;
}

.chat-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--gray-color);
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    margin-right: 10px;
    color: var(--text-color);
}

.chat-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-color);
}

@media (max-width: 768px) {
    .chat-title {
        width: 100%;
        /* 在小屏幕设备上，让容器宽度自适应 */
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

.chat-actions {
    display: flex;
    gap: 10px;
}

.action-btn-kldmx {
    background: none;
    border: 1px solid var(--gray-color);
    padding: 3px 5px;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.9rem;
    transition: all 0.2s;
}

.action-btn:hover {
    background-color: var(--gray-color);
}

.chat-container {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background-color: white;
    background-image:
        radial-gradient(circle at 10% 20%, rgba(233, 236, 239, 0.1) 0%, transparent 20%),
        radial-gradient(circle at 90% 80%, rgba(233, 236, 239, 0.1) 0%, transparent 20%);
}

/* 欢迎页面样式 */
.welcome-container {
    max-width: 850px;
    margin: 0 auto;
    padding: 40px 20px;
    text-align: center;
}

.welcome-logo {
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: white;
    font-size: 40px;
}

.welcome-title {
    font-size: 2.5rem;
    margin-top: 15px;
    margin-bottom: 15px;
    color: var(--primary-color);
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.welcome-subtitle {
    font-size: 1.2rem;
    color: var(--text-light);
    margin-bottom: 20px;
    max-width: 850px;
    margin-left: auto;
    margin-right: auto;
    font-size: 16px;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.feature-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 25px;
    box-shadow: var(--box-shadow);
    transition: transform 0.3s ease;
    border: 1px solid var(--gray-color);
    text-align: left;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, rgba(110, 72, 170, 0.1), rgba(157, 80, 187, 0.1));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    color: var(--primary-color);
    font-size: 24px;
}

.feature-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--primary-color);
}

.feature-description {
    color: var(--text-light);
    line-height: 1.6;
}

.examples-container {
    background: linear-gradient(135deg, rgba(110, 72, 170, 0.05), rgba(157, 80, 187, 0.05));
    border-radius: var(--border-radius);
    padding: 30px;
    margin: 40px 0;
    border: 1px solid rgba(110, 72, 170, 0.1);
}

.examples-title {
    font-size: 1.5rem;
    margin-bottom: 20px;
    color: var(--primary-color);
    text-align: center;
}

.examples-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
}

.example-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid var(--gray-color);
    text-align: left;
}

.example-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);
}

.example-text {
    color: var(--text-light);
    line-height: 1.6;
}

.start-button {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 30px;
    cursor: pointer;
    font-weight: 500;
    font-size: 1.1rem;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
    margin-top: 10px;
}

.start-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(110, 72, 170, 0.4);
}

.message {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
    max-width: 900px;
    margin-left: auto;
    margin-right: auto;
}

.message-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: var(--gray-color);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.message-user .message-avatar {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.message-content {
    flex: 1;
}

.message-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.message-author {
    font-weight: 600;
}

.message-time {
    color: var(--text-light);
    font-size: 0.8rem;
    margin-left: 10px;
}

.message-text {
    line-height: 1.6;
    padding: 12px 15px;
    background: #f8f9fa;
    border-radius: 12px;
    font-size: 16px;
    border: 1px solid #e9ecef;
}

@media (max-width: 768px) {
    .message-text {
        line-height: 1.6;
        padding: 12px 15px;
        background: #f8f9fa;
        border-radius: 12px;
        font-size: 14px;
        border: 1px solid #e9ecef;
    }
}

.message-ai .message-text {
    background: linear-gradient(135deg, rgba(110, 72, 170, 0.05), rgba(157, 80, 187, 0.05));
    border: 1px solid rgba(110, 72, 170, 0.1);
}

/* Markdown内容样式 */
.markdown-content {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
    margin-top: 5px;
    margin-bottom: 16px;
    font-weight: 600;
    line-height: 1.25;
    color: var(--primary-color);
}

/* 增强标题样式，添加图标支持 */
.markdown-content h3 {
    font-size: 1.25em;
    display: flex;
    align-items: center;
    gap: 8px;
}

.markdown-content h3::before {
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    font-size: 0.8em;
}

/* 数据类型相关的标题图标 - 通过JavaScript动态添加类名 */
.markdown-content h3.data-header::before {
    content: "\f1c0";
    color: var(--success-color);
}

.markdown-content h3.json-header::before {
    content: "\f1c9";
    color: var(--primary-color);
}

.markdown-content h3.code-header::before {
    content: "\f121";
    color: var(--secondary-color);
}

.markdown-content h3.error-header::before {
    content: "\f071";
    color: var(--danger-color);
}

.markdown-content h3.warning-header::before {
    content: "\f06a";
    color: var(--warning-color);
}

.markdown-content h3.timeout-header::before {
    content: "\f017";
    color: var(--warning-color);
}

.markdown-content h1 {
    font-size: 1.8em;
    padding-bottom: 0.3em;
    border-bottom: 1px solid var(--gray-color);
}

.markdown-content h2 {
    font-size: 1.5em;
    padding-bottom: 0.3em;
    border-bottom: 1px solid var(--gray-color);
}

.markdown-content h3 {
    font-size: 1.25em;
}

.markdown-content p {
    margin-bottom: 16px;
    line-height: 1.6;
}

.markdown-content ul,
.markdown-content ol {
    margin-bottom: 16px;
    padding-left: 24px;
}

.markdown-content li {
    margin-bottom: 4px;
}

.markdown-content strong {
    font-weight: 600;
    color: var(--text-color);
}

.markdown-content code {
    background-color: rgba(110, 72, 170, 0.1);
    padding: 0.2em 0.4em;
    border-radius: 3px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.9em;
    color: var(--primary-color);
    /* 行内代码换行优化 */
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.markdown-content pre {
    background: linear-gradient(135deg, #f6f8fa 0%, #f8f9fa 100%);
    border: 1px solid var(--gray-color);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    overflow-x: auto;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.9em;
    line-height: 1.5;
    position: relative;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

/* JSON代码块特殊样式 */
.markdown-content pre:has(code.language-json) {
    background: linear-gradient(135deg, rgba(110, 72, 170, 0.05) 0%, rgba(157, 80, 187, 0.05) 100%);
    border: 1px solid rgba(110, 72, 170, 0.2);
}

/* Python代码块特殊样式 */
.markdown-content pre:has(code.language-python) {
    background: linear-gradient(135deg, rgba(52, 168, 83, 0.05) 0%, rgba(52, 168, 83, 0.1) 100%);
    border: 1px solid rgba(52, 168, 83, 0.2);
}

/* SQL代码块特殊样式 */
.markdown-content pre:has(code.language-sql) {
    background: linear-gradient(135deg, rgba(255, 106, 0, 0.05) 0%, rgba(255, 106, 0, 0.1) 100%);
    border: 1px solid rgba(255, 106, 0, 0.2);
}

.markdown-content pre code {
    background: none;
    padding: 0;
    border-radius: 0;
    color: inherit;
    display: block;
    /* 优化代码换行 */
    white-space: pre-wrap;
    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: break-word;
    /* 保持代码的可读性 */
    line-height: 1.5;
    /* 确保长URL和长字符串能够换行 */
    hyphens: none;
}

/* 代码块复制按钮 */
.markdown-content pre::before {
    content: attr(data-lang);
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(255, 255, 255, 0.9);
    color: var(--text-light);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.7em;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 增强JSON语法高亮 */
.hljs-string {
    color: #d73a49;
}

.hljs-number {
    color: #005cc5;
}

.hljs-keyword {
    color: #d73a49;
}

.hljs-literal {
    color: #005cc5;
}

.hljs-attr {
    color: #6f42c1;
}

/* 表格样式 */
.markdown-content table {
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 16px;
    border: 1px solid var(--gray-color);
    border-radius: 6px;
    overflow: hidden;
}

.markdown-content table th,
.markdown-content table td {
    padding: 2px 2px; /*修改表格被撑开*/
    text-align: center;
    border-bottom: 1px solid var(--gray-color);
    border-right: 1px solid var(--gray-color);
}

.markdown-content table th {
    background-color: rgba(110, 72, 170, 0.1);
    font-weight: 600;
    color: var(--primary-color);
}

.markdown-content table tr:last-child td {
    border-bottom: none;
}

.markdown-content table th:last-child,
.markdown-content table td:last-child {
    border-right: none;
}

.markdown-content table tr:nth-child(even) {
    background-color: rgba(0, 0, 0, 0.02);
}

.markdown-content table tr:hover {
    background-color: rgba(110, 72, 170, 0.05);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .chat-actions{
        display: none;
    }
}

/* JSON数据表格特殊样式 */
.json-table-container {
    max-height: 500px;
    overflow-y: auto;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    margin: 16px 0;
    border: 1px solid rgba(110, 72, 170, 0.2);
}

.json-data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9em;
    background: white;
    margin: 0;
}

.json-data-table thead th {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    font-weight: 600;
    padding: 12px 10px;
    text-align: left;
    position: sticky;
    top: 0;
    z-index: 10;
}

.json-data-table tbody tr {
    transition: background-color 0.2s ease;
}

.json-data-table tbody tr:nth-child(even) {
    background-color: rgba(110, 72, 170, 0.02);
}

.json-data-table tbody tr:hover {
    background-color: rgba(110, 72, 170, 0.08);
    transform: scale(1.001);
}

.json-data-table td {
    padding: 10px;
    border-bottom: 1px solid rgba(110, 72, 170, 0.1);
    vertical-align: top;
}

/* 键值对表格样式 */
.key-value-table .key-cell {
    background-color: rgba(110, 72, 170, 0.05);
    font-weight: 600;
    color: var(--primary-color);
    width: 30%;
}

/* 数据类型样式 */
.string-value {
    color: #d73a49;
}

.number-value {
    color: #005cc5;
    font-weight: 600;
}

.bool-value {
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.85em;
}

.bool-true {
    background-color: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.bool-false {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.null-value {
    color: #6f42c1;
    font-style: italic;
    opacity: 0.7;
}

.date-value {
    color: #e36209;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 0.9em;
}

.url-value {
    color: var(--primary-color);
    text-decoration: none;
}

.url-value:hover {
    text-decoration: underline;
}

.complex-value {
    background-color: rgba(110, 72, 170, 0.1);
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 0.85em;
    color: var(--primary-color);
    cursor: help;
}

.other-value {
    color: var(--text-color);
}

/* details 和 summary 样式优化 */
.markdown-content details {
    border: 1px solid rgba(110, 72, 170, 0.2);
    border-radius: 6px;
    padding: 12px;
    margin: 16px 0;
    background-color: rgba(110, 72, 170, 0.02);
}

.markdown-content details summary {
    cursor: pointer;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 8px;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.markdown-content details summary:hover {
    background-color: rgba(110, 72, 170, 0.1);
}

.markdown-content details[open] summary {
    margin-bottom: 12px;
    border-bottom: 1px solid rgba(110, 72, 170, 0.2);
}

/* 表格响应式设计 */
@media (max-width: 768px) {
    .json-table-container {
        max-height: 300px;
    }

    .json-data-table {
        font-size: 0.8em;
    }

    .json-data-table th,
    .json-data-table td {
        padding: 8px 6px;
    }

    .key-value-table .key-cell {
        width: 40%;
    }
}

/* 折叠区域样式 */
.markdown-content details {
    border: 1px solid var(--gray-color);
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 16px;
    background-color: rgba(110, 72, 170, 0.02);
}

.markdown-content details summary {
    cursor: pointer;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 8px;
}

.markdown-content details[open] summary {
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--gray-color);
}

/* 引用块样式 */
.markdown-content blockquote {
    border-left: 4px solid var(--primary-color);
    padding-left: 16px;
    margin: 16px 0;
    color: var(--text-light);
    font-style: italic;
    background-color: rgba(110, 72, 170, 0.05);
    padding: 16px;
    border-radius: 0 6px 6px 0;
}

/* 水平线样式 */
.markdown-content hr {
    border: none;
    border-top: 1px solid var(--gray-color);
    margin: 24px 0;
}

/* 链接样式 */
.markdown-content a {
    color: var(--primary-color);
    text-decoration: none;
}

.markdown-content a:hover {
    text-decoration: underline;
}

/* 数据统计信息样式 */
.markdown-content p strong {
    padding: 2px 6px;
    background: rgba(110, 72, 170, 0.1);
    border-radius: 4px;
    font-size: 0.9em;
}

/* 数据类型标签样式 */
.data-type-badge {
    display: inline-block;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: 600;
    margin-left: 8px;
}

/* 特殊内容容器 */
.data-container {
    background: linear-gradient(135deg, rgba(110, 72, 170, 0.02) 0%, rgba(157, 80, 187, 0.02) 100%);
    border: 1px solid rgba(110, 72, 170, 0.15);
    border-radius: 12px;
    padding: 20px;
    margin: 16px 0;
    position: relative;
}

.data-container::before {
    content: "";
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 12px;
    z-index: -1;
    opacity: 0.1;
}

/* JSON折叠展开功能 */
.json-collapsible {
    max-height: 300px;
    overflow: hidden;
    transition: max-height 0.3s ease;
    position: relative;
}

.json-collapsible.expanded {
    max-height: none;
}

.json-expand-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9em;
    margin-top: 10px;
    transition: all 0.2s;
}

.json-expand-btn:hover {
    opacity: 0.9;
    transform: translateY(-1px);
}

/* 表格增强样式 */
.data-table-container {
    max-height: 400px;
    overflow-y: auto;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin: 16px 0;
}

/* 响应式设计增强 */
@media (max-width: 768px) {
    .markdown-content pre {
        padding: 12px;
        font-size: 0.8em;
        /* 移动端强制换行 */
        overflow-x: auto;
        word-break: break-all;
    }

    .markdown-content pre code {
        /* 移动端代码块强制换行 */
        white-space: pre-wrap !important;
        word-wrap: break-word !important;
        overflow-wrap: break-word !important;
        word-break: break-all;
        /* 确保长单词能够换行 */
        hyphens: auto;
        /* 保持代码的可读性 */
        line-height: 1.4;
    }

    .data-container {
        padding: 12px;
        margin: 12px 0;
    }

    .json-collapsible {
        max-height: 200px;
    }
}

.message-actions {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.message-action-btn {
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 4px;
}

.message-action-btn:hover {
    color: var(--primary-color);
}

.input-container {
    padding: 10px;
    background-color: white;
    border-top: 1px solid var(--gray-color);
}

.input-box {
    max-width: 900px;
    margin: 0 auto;
    position: relative;
}

.input-tools {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 0px;
}

.tool-btn {
    background: none;
    border: 1px solid var(--gray-color);
    padding: 6px 12px;
    border-radius: 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.9rem;
    transition: all 0.2s;
}

.tool-btn:hover {
    background-color: var(--gray-color);
}

.tool-btn.active {
    color: var(--primary-color);
    background-color: rgba(110, 72, 170, 0.1);
    border-color: rgba(110, 72, 170, 0.2);
}

.file-input {
    display: none;
}

.prompt-hint {
    background-color: var(--gray-color);
    padding: 10px 15px;
    border-radius: var(--border-radius);
    margin-bottom: 10px;
    font-size: 0.9rem;
    color: var(--text-light);
}

.prompt-hint strong {
    color: var(--text-color);
}

.text-input {
    width: 100%;
    min-height: 80px;
    max-height: 200px;
    padding: 10px 120px 10px 20px;
    border: none;
    background: transparent;
    resize: none;
    font-size: 1rem;
    outline: none;
    transition: var(--transition);
    line-height: 1.5;
    color: var(--text-primary);

}

/* .text-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(110, 72, 170, 0.2);
} */

.send-btn {
    position: absolute;
    right: 10px;
    bottom: 10px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    width: 42px;
    height: 42px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.send-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(110, 72, 170, 0.3);
}

.send-btn:disabled {
    background: var(--gray-color);
    cursor: not-allowed;
}

/* 清空按钮样式 */
.clear-btn {
    position: absolute;
    right: 62px;
    bottom: 10px;
    background: linear-gradient(135deg, #6c757d, #5a6268);
    color: white;
    border: none;
    width: 42px;
    height: 42px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
    font-size: 14px;
}

.clear-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);
    background: linear-gradient(135deg, #dc3545, #c82333);
}

.clear-btn:active {
    transform: scale(0.95);
}
/* 优化新闻资讯样式 */
.news-ticker {
    background: #f0f4f8;
    border-bottom: 1px solid #e1e5e9;
    padding: 8px 10px;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    height: 40px;
    /* 3行高度 (24px * 3) */
}

.news-label {
    background: #005cc5;
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 6px;
    margin-right: 15px;
    flex-shrink: 0;
}

.news-container {
    flex: 1;
    overflow: hidden;
    position: relative;
    height: 100%;
}

.news-scroll {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    text-align: left;
    animation: scrollNews 15s linear infinite;
}

.news-item {
    height: 24px;
    line-height: 24px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 0;
}

.news-item .hot {
    color: #d73a49;
    font-size: 16px;
    margin-right: 6px;
    font-weight: bold;
}

.news-item a {
    color: #005cc5;
    font-size: 16px;
    text-decoration: none;
}

.news-item a:hover {
    color: var(--primary-color);

    text-decoration: underline;
}

@keyframes scrollNews {
    0% {
        transform: translateY(0);
    }

    10% {
        transform: translateY(0);
        /* 初始暂停 */
    }

    25% {
        transform: translateY(-24px);
        /* 滚动第一条 */
    }

    40% {
        transform: translateY(-24px);
        /* 暂停 */
    }

    55% {
        transform: translateY(-48px);
        /* 滚动第二条 */
    }

    70% {
        transform: translateY(-48px);
        /* 暂停 */
    }

    85% {
        transform: translateY(-72px);
        /* 滚动第三条 */
    }

    100% {
        transform: translateY(-72px);
        /* 结束位置 */
    }
}

/* 无缝滚动效果 */
.news-scroll::after {
    content: "";
    position: absolute;
    top: 72px;
    /* 在内容下方 */
    left: 0;
    width: 100%;
    height: 100%;
    background: inherit;
    background-image: inherit;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        left: -280px;
        z-index: 1000;
        height: 100%;
        max-height: 100vh; /* 确保移动端也不会超出视窗 */
        overflow: hidden; /* 防止移动端内容溢出 */
    }

    .sidebar.active {
        left: 0;
    }

    .sidebar-toggle {
        display: none;
    }

    .menu-toggle {
        display: block;
    }

    /* 移动端历史记录容器优化 */
    .history-container {
        max-height: calc(100vh - 180px); /* 移动端调整高度 */
        overflow-y: auto;
    }

    .welcome-title {
        font-size: 2rem;
    }

    .news-ticker {
        display: none;
    }
    .features-grid {
        display: none;
    }
    .start-button {
        display: none;
    }
    .features-grid {
        grid-template-columns: 1fr;
    }
}

/* 加载动画 */
.typing-indicator {
    display: flex;
    padding: 10px 0;
}

.typing-dot {
    width: 8px;
    height: 8px;
    background-color: var(--text-light);
    border-radius: 50%;
    margin: 0 2px;
    animation: typingAnimation 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
    animation-delay: 0s;
}

.typing-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typingAnimation {

    0%,
    60%,
    100% {
        transform: translateY(0);
    }

    30% {
        transform: translateY(-5px);
    }
}

/* 附件样式 */
.attachment {
    display: inline-flex;
    align-items: center;
    background-color: var(--gray-color);
    padding: 6px 10px;
    border-radius: 20px;
    margin-top: 8px;
    font-size: 0.9rem;
    margin-right: 8px;
}

.attachment-icon {
    margin-right: 6px;
}

.attachment-name {
    margin-right: 8px;
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.attachment-remove {
    color: var(--text-light);
    cursor: pointer;
}

.attachment-remove:hover {
    color: var(--danger-color);
}

/* 深色模式切换 */
.mode-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    z-index: 99;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c5c5c5;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

.sidebar ::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.sidebar ::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
}

.sidebar ::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

.no-underline {
    text-decoration: none;
}

a {
    color: inherit;
    /* 保持原始颜色 */
}

a:hover {
    color: inherit;
    /* 鼠标悬停时的颜色 */
}

/*复制按钮*/
.action-btn-kb {
    background: none;
    border: none;
    color: var(--gray);
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
}

.action-btn-kb:hover {
    color: var(--primary);
}

.input-box {
    position: relative;
    border-radius: var(--radius);
    background: var(--bg-light);
    border: 1px solid var(--border);
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.input-box:focus-within {
    background: var(--bg-input);
    border-color: var(--border-focus);
    box-shadow: var(--shadow-focus);
}

/* 输入区域 - DeepSeek风格 */
.attachmentsContainer {
    padding: 5px 16px 5px;
    background-color: #fff9fb;
    position: relative;
    border: 1px solid var(--gray-color);
    border-radius: 30px;
    resize: none;
    font-size: 1rem;
    outline: none;
    transition: all 0.2s;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

/* 流式API相关样式 */
.stream-cursor {
    animation: blink 1s infinite;
    color: #007bff;
    font-weight: bold;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

.stream-table {
    margin: 1rem 0;
    border-collapse: collapse;
    width: 100%;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

.stream-table th {
    background-color: #f8f9fa;
    padding: 12px;
    text-align: left;
    font-weight: 600;
    border-bottom: 2px solid #e0e0e0;
}

.stream-table td {
    padding: 10px 12px;
    border-bottom: 1px solid #f0f0f0;
}

.stream-table tr:nth-child(even) {
    background-color: #f9f9f9;
}

.stream-table tr:hover {
    background-color: #f0f8ff;
}

.key-value-table .key-cell {
    font-weight: 600;
    background-color: #f8f9fa;
    width: 30%;
}

.table-fallback {
    background-color: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
}

.table-fallback h4 {
    margin: 0 0 0.5rem 0;
    color: #495057;
}

.plot-container {
    background-color: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
}

.plot-container h4 {
    margin: 0 0 0.5rem 0;
    color: #495057;
}

.plot-container details {
    margin-top: 0.5rem;
}

.plot-container summary {
    cursor: pointer;
    font-weight: 500;
    color: #007bff;
}

.plot-container summary:hover {
    text-decoration: underline;
}

/* 优化消息显示 */
.message-content .message-text {
    line-height: 1.6;
}

.message-content .message-text p {
    margin: 0.5rem 0;
}

.message-content .message-text p:first-child {
    margin-top: 0;
}

.message-content .message-text p:last-child {
    margin-bottom: 0;
}

/* Plotly图表样式 */
.plotly-chart {
    width: 100% !important;
    min-height: 400px;
    margin: 15px 0;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background-color: white;
    overflow: hidden;
}

.plot-fallback {
    padding: 15px;
    margin: 15px 0;
    border: 1px solid #ffa500;
    border-radius: 8px;
    background-color: #fffbf0;
}

.plot-fallback summary {
    cursor: pointer;
    font-weight: 500;
    margin-bottom: 10px;
}

.plot-fallback pre {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    overflow-x: auto;
    font-size: 12px;
    line-height: 1.4;
}

/* 流式表格增强样式 */
.stream-table {
    margin: 15px 0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stream-table th {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    font-weight: 600;
    text-align: left;
}

.stream-table td {
    border-bottom: 1px solid #e9ecef;
}

.stream-table tr:last-child td {
    border-bottom: none;
}

/* 确保AI消息中的表格和组件有正确的背景色 */
.message-ai .message-text .stream-table,
.message-ai .message-text .table-fallback,
.message-ai .message-text .collapsible-table-container,
.message-ai .message-text .history-table-container {
    background: transparent;
}

.message-ai .message-text .stream-table th {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.message-ai .message-text .stream-table td {
    background: transparent;
}

/* 可折叠表格基本样式 */
.collapsible-table-container {
    margin: 10px 0;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    background: #f8f9fa;
}

.collapsible-table-container .table-header {
    padding: 12px 16px;
    background: #f1f3f4;
    border-bottom: 1px solid #e0e0e0;
    cursor: pointer;
    user-select: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.collapsible-table-container .table-content {
    padding: 16px;
    max-height: 400px;
    overflow-y: auto;
}

/* 历史表格容器样式 */
.history-table-container {
    margin: 10px 0;
}

/* 可折叠表格在AI消息中的样式调整 */
.message-ai .message-text .collapsible-table-container {
    background: rgba(110, 72, 170, 0.03) !important;
    border-color: rgba(110, 72, 170, 0.15) !important;
}

.message-ai .message-text .collapsible-table-container .table-header {
    background: rgba(110, 72, 170, 0.08) !important;
    border-color: rgba(110, 72, 170, 0.15) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .plotly-chart {
        min-height: 300px;
        margin: 10px 0;
    }
    
    .stream-table {
        font-size: 14px;
    }
    
    .stream-table th,
    .stream-table td {
        padding: 8px;
    }
}

/* HTML图表容器样式 */
.html-chart-container {
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin: 15px 0;
    padding: 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;
    overflow: hidden;
}

.html-chart-container:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* 确保HTML图表内容适应容器 */
.html-chart-container iframe,
.html-chart-container canvas,
.html-chart-container svg,
.html-chart-container div {
    width: 100% !important;
    max-width: 100% !important;
    border: none;
}

/* 暗色模式下的HTML图表容器 */
.dark-mode .html-chart-container {
    background: #2d2d2d;
    border-color: #444444;
}

/* 图表容器内的滚动条美化 */
.html-chart-container::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.html-chart-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.html-chart-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.html-chart-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.dark-mode .html-chart-container::-webkit-scrollbar-track {
    background: #444444;
}

.dark-mode .html-chart-container::-webkit-scrollbar-thumb {
    background: #666666;
}

.dark-mode .html-chart-container::-webkit-scrollbar-thumb:hover {
    background: #777777;
}

/* 安全警告样式已移至 templates/index.html 中统一管理 */

/* 输入框安全状态指示 */
.input-security-indicator {
    position: absolute;
    right: 20px;
    top: 15px;
    display: none;
    align-items: center;
    gap: 6px;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    z-index: 10;
}

.input-security-indicator.safe {
    background: rgba(40, 167, 69, 0.15);
    color: var(--success-color);
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.input-security-indicator.warning {
    background: rgba(255, 193, 7, 0.15);
    color: var(--warning-color);
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.input-security-indicator.danger {
    background: rgba(220, 53, 69, 0.15);
    color: var(--danger-color);
    border: 1px solid rgba(220, 53, 69, 0.3);
}

.input-security-indicator i {
    font-size: 10px;
}

/* 指示器动画效果 */
.input-security-indicator {
    transition: all 0.2s ease;
    opacity: 0.9;
}

.input-security-indicator:hover {
    opacity: 1;
    transform: scale(1.05);
}

.input-char-counter {
    transition: all 0.2s ease;
}

.input-char-counter:hover {
    transform: scale(1.02);
}

/* 字数统计样式 */
.input-char-counter {
    position: absolute;
    right: 20px;
    bottom: 62px;
    font-size: 11px;
    color: var(--text-light);
    background: rgba(255, 255, 255, 0.95);
    padding: 3px 8px;
    border-radius: 12px;
    pointer-events: auto;
    z-index: 10;
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 6px;
}

.char-clean-btn {
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    padding: 2px;
    border-radius: 4px;
    font-size: 10px;
    transition: all 0.2s ease;
    opacity: 0.7;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
}

.char-clean-btn:hover {
    background: rgba(0, 0, 0, 0.1);
    opacity: 1;
    transform: scale(1.1);
}

.char-clean-btn:active {
    transform: scale(0.95);
}

.input-char-counter.warning {
    color: var(--warning-color);
    background: rgba(255, 193, 7, 0.15);
    border-color: rgba(255, 193, 7, 0.3);
    font-weight: 500;
}

.input-char-counter.danger {
    color: var(--danger-color);
    background: rgba(220, 53, 69, 0.15);
    border-color: rgba(220, 53, 69, 0.3);
    font-weight: 600;
    animation: charCounterPulse 1s ease-in-out infinite alternate;
}

@keyframes charCounterPulse {
    from {
        opacity: 0.8;
    }
    to {
        opacity: 1;
    }
}

/* 响应式设计 - 字数统计 */
@media (max-width: 768px) {
    .input-security-indicator {
        right: 15px;
        top: 12px;
        font-size: 10px;
        padding: 2px 6px;
        gap: 4px;
    }

    .input-char-counter {
        font-size: 10px;
        padding: 2px 6px;
        right: 15px;
        bottom: 50px;
    }

    .send-btn {
        width: 36px;
        height: 36px;
        bottom: 8px;
        right: 8px;
    }

    .clear-btn {
        width: 36px;
        height: 36px;
        bottom: 8px;
        right: 50px;
        font-size: 12px;
    }

    .text-input {
        padding: 8px 100px 8px 15px;
        min-height: 70px;
    }
}

@media (max-width: 480px) {
    .input-security-indicator {
        right: 12px;
        font-size: 9px;
        padding: 1px 4px;
    }

    .input-char-counter {
        font-size: 9px;
        padding: 1px 4px;
        right: 12px;
        bottom: 44px;
    }

    .send-btn {
        width: 32px;
        height: 32px;
        right: 6px;
    }

    .clear-btn {
        width: 32px;
        height: 32px;
        right: 44px;
        font-size: 11px;
    }

    .text-input {
        padding: 6px 85px 6px 12px;
        min-height: 60px;
        font-size: 14px;
    }

    /* 小屏幕设备的代码块优化 */
    .markdown-content pre {
        padding: 8px;
        font-size: 0.75em;
        margin: 8px 0;
        border-radius: 6px;
    }

    .markdown-content pre code {
        /* 小屏幕强制换行 */
        white-space: pre-wrap !important;
        word-wrap: break-word !important;
        overflow-wrap: break-word !important;
        word-break: break-all;
        line-height: 1.3;
        /* 调整字体大小以适应小屏幕 */
        font-size: 0.9em;
    }

    /* 行内代码在小屏幕上的优化 */
    .markdown-content code {
        word-break: break-all;
        overflow-wrap: break-word;
    }
}

/* 移动端安全警告样式优化 */
@media (max-width: 768px) {
    .security-warning {
        max-width: 90%;
        width: 90%;
        margin: 0 auto;
    }
    
    .security-warning-header {
        padding: 14px;
    }
    
    .security-warning-title {
        font-size: 15px;
    }
    
    .security-warning-icon {
        font-size: 18px;
    }
    
    .security-warning-body {
        padding: 16px;
    }
    
    .security-warning-message {
        font-size: 13px;
    }
    
    .security-warning-suggestions {
        padding: 10px;
        margin-bottom: 12px;
    }
    
    .security-warning-suggestions h4 {
        font-size: 12px;
    }
    
    .security-warning-suggestions li {
        font-size: 12px;
    }
    
    .security-cooldown {
        padding: 10px;
        margin-bottom: 12px;
    }
    
    .security-cooldown-time {
        font-size: 20px;
    }
    
    .security-warning-actions {
        flex-direction: column;
        gap: 8px;
    }
    
    .security-warning-btn {
        width: 100%;
        padding: 10px 14px;
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .security-warning {
        max-width: 95%;
        width: 95%;
        margin: 0 auto;
    }
    
    .security-warning-header {
        padding: 12px;
    }
    
    .security-warning-title {
        font-size: 14px;
    }
    
    .security-warning-icon {
        font-size: 16px;
    }
    
    .security-warning-body {
        padding: 14px;
    }
    
    .security-warning-message {
        font-size: 12px;
        line-height: 1.4;
    }
    
    .security-warning-suggestions {
        padding: 8px;
        margin-bottom: 10px;
    }
    
    .security-warning-suggestions h4 {
        font-size: 11px;
        margin-bottom: 6px;
    }
    
    .security-warning-suggestions li {
        font-size: 11px;
        margin-bottom: 4px;
    }
    
    .security-cooldown {
        padding: 8px;
        margin-bottom: 10px;
    }
    
    .security-cooldown-time {
        font-size: 18px;
        letter-spacing: 0.5px;
    }
    
    .security-cooldown-label {
        font-size: 10px;
    }
    
    .security-warning-btn {
        padding: 9px 12px;
        font-size: 12px;
    }
}