{"timestamp": "2025-06-30T09:35:42.140247", "level": "INFO", "logger": "api", "message": "API调用 - POST http://api.example.com/chat (状态码: 200) (耗时: 1.500s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19680, "thread_name": "MainThread", "user_id": "user123", "operation": "api_call", "duration": 1.5}
{"timestamp": "2025-06-30T09:35:42.141016", "level": "INFO", "logger": "api", "message": "API调用 - POST http://api.example.com/chat (状态码: 500) (耗时: 30.000s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19680, "thread_name": "MainThread", "user_id": "user123", "operation": "api_call", "duration": 30.0}
{"timestamp": "2025-06-30T09:49:47.561661", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.112:8211/api/query/ (状态码: 200) (耗时: 27.918s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20476, "thread_name": "Thread-16 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 27.918116331100464}
{"timestamp": "2025-06-30T10:04:45.829859", "level": "INFO", "logger": "api", "message": "API调用 - POST http://api.example.com/chat (状态码: 200) (耗时: 1.500s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 17800, "thread_name": "MainThread", "user_id": "user123", "operation": "api_call", "duration": 1.5}
{"timestamp": "2025-06-30T10:04:45.831329", "level": "INFO", "logger": "api", "message": "API调用 - POST http://api.example.com/chat (状态码: 500) (耗时: 30.000s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 17800, "thread_name": "MainThread", "user_id": "user123", "operation": "api_call", "duration": 30.0}
{"timestamp": "2025-06-30T10:14:41.997499", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.112:8211/api/query/ (状态码: 200) (耗时: 21.064s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19572, "thread_name": "Thread-30 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 21.063926219940186}
{"timestamp": "2025-06-30T10:18:16.070316", "level": "INFO", "logger": "api", "message": "API调用 - POST http://api.example.com/chat (状态码: 200) (耗时: 1.500s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 2240, "thread_name": "MainThread", "user_id": "user123", "operation": "api_call", "duration": 1.5}
{"timestamp": "2025-06-30T10:18:16.070951", "level": "INFO", "logger": "api", "message": "API调用 - POST http://api.example.com/chat (状态码: 500) (耗时: 30.000s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 2240, "thread_name": "MainThread", "user_id": "user123", "operation": "api_call", "duration": 30.0}
{"timestamp": "2025-06-30T17:22:05.514745", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 0.035s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 3960, "thread_name": "Thread-15 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 0.03485465049743652}
{"timestamp": "2025-06-30T17:30:52.127962", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 0.007s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 21164, "thread_name": "Thread-35 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 0.007030487060546875}
{"timestamp": "2025-06-30T17:41:29.653319", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 9.917s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18704, "thread_name": "Thread-2 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 9.916568517684937}
{"timestamp": "2025-06-30T17:41:34.053147", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 0.006s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 12536, "thread_name": "Thread-3 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 0.006462812423706055}
{"timestamp": "2025-06-30T17:42:54.240374", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 0.006s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 14096, "thread_name": "Thread-19 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 0.005769968032836914}
{"timestamp": "2025-06-30T17:50:46.439353", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 8.976s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13444, "thread_name": "Thread-16 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 8.975532293319702}
{"timestamp": "2025-06-30T17:50:53.054877", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 0.007s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 17496, "thread_name": "Thread-17 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 0.006655216217041016}
{"timestamp": "2025-06-30T17:54:51.249387", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 32.982s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 17776, "thread_name": "Thread-7 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 32.98245620727539}
{"timestamp": "2025-06-30T17:55:35.310142", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 32.939s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 11112, "thread_name": "Thread-10 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 32.93855023384094}
{"timestamp": "2025-06-30T17:56:17.578021", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 31.302s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 11592, "thread_name": "Thread-13 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 31.30162501335144}
{"timestamp": "2025-07-01T15:07:31.584263", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 26.934s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 1532, "thread_name": "Thread-15 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 26.933722734451294}
{"timestamp": "2025-07-01T15:32:24.134145", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 31.462s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20964, "thread_name": "Thread-30 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 31.462137699127197}
{"timestamp": "2025-07-01T15:34:33.057482", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 31.415s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 16420, "thread_name": "Thread-15 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 31.414666175842285}
{"timestamp": "2025-07-01T15:36:56.017111", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 34.152s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 16108, "thread_name": "Thread-31 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 34.15218997001648}
{"timestamp": "2025-07-01T15:37:47.636751", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 13.022s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 21356, "thread_name": "Thread-34 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 13.022438526153564}
{"timestamp": "2025-07-01T15:39:07.796223", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 28.755s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 16748, "thread_name": "Thread-36 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 28.754692554473877}
{"timestamp": "2025-07-01T15:55:39.161525", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 29.031s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19732, "thread_name": "Thread-19 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 29.03052854537964}
{"timestamp": "2025-07-01T15:56:30.086181", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 33.372s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20416, "thread_name": "Thread-33 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 33.372337102890015}
{"timestamp": "2025-07-01T16:04:51.588761", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 26.425s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 15804, "thread_name": "Thread-19 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 26.425015926361084}
{"timestamp": "2025-07-01T16:05:18.795732", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 13.690s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13296, "thread_name": "Thread-22 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 13.690457344055176}
{"timestamp": "2025-07-01T16:05:41.692388", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 21.091s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19396, "thread_name": "Thread-24 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 21.09130024909973}
{"timestamp": "2025-07-01T16:08:55.809734", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 33.439s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 12080, "thread_name": "Thread-37 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 33.43898677825928}
{"timestamp": "2025-07-01T16:10:07.104439", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 35.061s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 14024, "thread_name": "Thread-56 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 35.06057953834534}
{"timestamp": "2025-07-01T16:10:47.973852", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 36.037s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 2972, "thread_name": "Thread-59 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 36.036733865737915}
{"timestamp": "2025-07-01T16:11:07.527005", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 18.362s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13268, "thread_name": "Thread-61 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 18.362026691436768}
{"timestamp": "2025-07-01T16:11:50.330967", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 19.879s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 7972, "thread_name": "Thread-63 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 19.87854552268982}
{"timestamp": "2025-07-01T16:16:45.465698", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 18.041s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20040, "thread_name": "Thread-65 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 18.041222095489502}
{"timestamp": "2025-07-01T16:28:24.720993", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 31.441s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 11692, "thread_name": "Thread-15 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 31.441425800323486}
{"timestamp": "2025-07-01T16:28:49.987318", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 10.562s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 2056, "thread_name": "Thread-22 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 10.561810970306396}
{"timestamp": "2025-07-01T16:29:17.699852", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 22.135s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 2748, "thread_name": "Thread-24 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 22.134896993637085}
{"timestamp": "2025-07-01T16:39:35.336868", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 32.977s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 17968, "thread_name": "Thread-15 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 32.977120876312256}
{"timestamp": "2025-07-01T16:44:53.860949", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 36.364s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13000, "thread_name": "Thread-15 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 36.36393713951111}
{"timestamp": "2025-07-01T16:45:13.642255", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 10.952s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18840, "thread_name": "Thread-18 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 10.952492952346802}
{"timestamp": "2025-07-01T16:46:03.447113", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 17.195s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 7064, "thread_name": "Thread-20 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 17.1950466632843}
{"timestamp": "2025-07-01T16:53:55.955051", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 31.341s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19556, "thread_name": "Thread-16 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 31.34136176109314}
{"timestamp": "2025-07-01T16:54:10.922478", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 12.423s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 4680, "thread_name": "Thread-19 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 12.422661781311035}
{"timestamp": "2025-07-01T16:54:32.893882", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 19.982s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18912, "thread_name": "Thread-21 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 19.982264518737793}
{"timestamp": "2025-07-01T16:56:52.735718", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 29.055s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 2248, "thread_name": "Thread-28 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 29.054874897003174}
{"timestamp": "2025-07-01T17:08:25.321819", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 5.961s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 8704, "thread_name": "Thread-2 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 5.9613401889801025}
{"timestamp": "2025-07-01T17:08:53.711941", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 5.423s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 9332, "thread_name": "Thread-4 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 5.42256760597229}
{"timestamp": "2025-07-01T17:10:40.988428", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 34.576s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 12660, "thread_name": "Thread-15 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 34.576003551483154}
{"timestamp": "2025-07-01T17:11:28.250144", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 13.812s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19532, "thread_name": "Thread-2 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 13.811624765396118}
{"timestamp": "2025-07-01T17:11:40.778067", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 10.701s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20680, "thread_name": "Thread-4 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 10.700887680053711}
{"timestamp": "2025-07-01T17:13:28.272050", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 28.198s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19736, "thread_name": "Thread-15 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 28.198022603988647}
{"timestamp": "2025-07-01T17:13:49.680201", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 14.611s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 6048, "thread_name": "Thread-18 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 14.611347198486328}
{"timestamp": "2025-07-01T17:14:17.162095", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 24.866s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20556, "thread_name": "Thread-20 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 24.866382360458374}
{"timestamp": "2025-07-01T17:20:13.028432", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 31.006s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 9188, "thread_name": "Thread-15 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 31.006023406982422}
{"timestamp": "2025-07-01T17:20:26.414271", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 11.824s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 17160, "thread_name": "Thread-18 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 11.8238205909729}
{"timestamp": "2025-07-01T17:20:53.399536", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 25.780s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 2320, "thread_name": "Thread-20 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 25.779524326324463}
{"timestamp": "2025-07-01T17:23:13.582150", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 32.661s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 2660, "thread_name": "Thread-41 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 32.66115045547485}
{"timestamp": "2025-07-01T17:23:41.237216", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 22.762s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 21008, "thread_name": "Thread-44 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 22.761804580688477}
{"timestamp": "2025-07-01T17:28:24.632571", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 32.418s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 10356, "thread_name": "Thread-15 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 32.418351888656616}
{"timestamp": "2025-07-01T17:29:51.145099", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 66.032s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20768, "thread_name": "Thread-18 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 66.03174448013306}
{"timestamp": "2025-07-01T17:31:44.061164", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 38.954s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 6368, "thread_name": "Thread-34 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 38.95447301864624}
{"timestamp": "2025-07-01T17:32:01.681904", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 11.579s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 11280, "thread_name": "Thread-37 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 11.578824281692505}
{"timestamp": "2025-07-01T17:32:37.215567", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 32.818s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 11500, "thread_name": "Thread-39 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 32.818278312683105}
{"timestamp": "2025-07-02T08:43:06.040359", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 28.488s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 5404, "thread_name": "Thread-14 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 28.488412857055664}
{"timestamp": "2025-07-02T08:43:20.229916", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 8.555s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 10852, "thread_name": "Thread-17 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 8.554596424102783}
{"timestamp": "2025-07-02T08:43:37.010040", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 13.759s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 1576, "thread_name": "Thread-19 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 13.759379386901855}
{"timestamp": "2025-07-02T09:34:59.515620", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream", "module": "logger_config", "function": "log_api_call", "line": 206, "thread": 14860, "thread_name": "Thread-6 (process_request_thread)", "user_id": "stream_user", "session_id": "stream_stable_abde11ed", "operation": "api_call", "duration": null}
{"timestamp": "2025-07-02T09:35:24.823102", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 25.308s)", "module": "logger_config", "function": "log_api_call", "line": 206, "thread": 14860, "thread_name": "Thread-6 (process_request_thread)", "user_id": "stream_user", "session_id": "stream_stable_abde11ed", "operation": "api_call", "duration": 25.307596921920776}
{"timestamp": "2025-07-02T09:36:09.732079", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream", "module": "logger_config", "function": "log_api_call", "line": 206, "thread": 2288, "thread_name": "Thread-9 (process_request_thread)", "user_id": "stream_user", "session_id": "stream_stable_abde11ed", "operation": "api_call", "duration": null}
{"timestamp": "2025-07-02T09:36:39.655788", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 29.924s)", "module": "logger_config", "function": "log_api_call", "line": 206, "thread": 2288, "thread_name": "Thread-9 (process_request_thread)", "user_id": "stream_user", "session_id": "stream_stable_abde11ed", "operation": "api_call", "duration": 29.923738956451416}
{"timestamp": "2025-07-02T09:36:51.639831", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream", "module": "logger_config", "function": "log_api_call", "line": 206, "thread": 9784, "thread_name": "Thread-11 (process_request_thread)", "user_id": "stream_user", "session_id": "stream_stable_abde11ed", "operation": "api_call", "duration": null}
{"timestamp": "2025-07-02T09:37:34.005001", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 42.365s)", "module": "logger_config", "function": "log_api_call", "line": 206, "thread": 9784, "thread_name": "Thread-11 (process_request_thread)", "user_id": "stream_user", "session_id": "stream_stable_abde11ed", "operation": "api_call", "duration": 42.3652126789093}
{"timestamp": "2025-07-02T10:18:51.919445", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 26.009s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 10660, "thread_name": "Thread-15 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 26.00869584083557}
{"timestamp": "2025-07-02T10:19:05.592819", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 12.199s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 14316, "thread_name": "Thread-18 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 12.198621273040771}
{"timestamp": "2025-07-02T10:19:26.034407", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 19.254s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 8644, "thread_name": "Thread-20 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 19.253655195236206}
{"timestamp": "2025-07-02T10:29:17.029936", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 24.840s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 15424, "thread_name": "Thread-19 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 24.84040665626526}
{"timestamp": "2025-07-02T10:29:33.345851", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 9.875s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13996, "thread_name": "Thread-27 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 9.874983310699463}
{"timestamp": "2025-07-02T10:30:10.349479", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 35.711s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18668, "thread_name": "Thread-29 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 35.711368560791016}
{"timestamp": "2025-07-02T10:38:13.730351", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 26.801s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 10948, "thread_name": "Thread-69 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 26.801270008087158}
{"timestamp": "2025-07-02T10:38:40.827744", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 15.999s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 3556, "thread_name": "Thread-72 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 15.999033451080322}
{"timestamp": "2025-07-02T10:39:28.981124", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 36.637s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 15128, "thread_name": "Thread-74 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 36.63670063018799}
{"timestamp": "2025-07-02T10:42:52.293155", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 6.886s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 9348, "thread_name": "Thread-95 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 6.885666847229004}
{"timestamp": "2025-07-02T10:43:27.456789", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 26.300s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19308, "thread_name": "Thread-111 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 26.299545526504517}
{"timestamp": "2025-07-02T10:44:31.779000", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 9.680s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 10052, "thread_name": "Thread-114 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 9.679958581924438}
{"timestamp": "2025-07-02T10:45:10.630089", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 32.298s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 7012, "thread_name": "Thread-116 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 32.29842281341553}
{"timestamp": "2025-07-02T10:51:37.337072", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 28.330s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 12072, "thread_name": "Thread-186 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 28.329657793045044}
{"timestamp": "2025-07-02T10:51:45.380954", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 31.985s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 16456, "thread_name": "Thread-188 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 31.9851291179657}
{"timestamp": "2025-07-02T10:52:09.403581", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 9.068s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 15712, "thread_name": "Thread-192 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 9.068202257156372}
{"timestamp": "2025-07-02T10:52:54.045806", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 39.727s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 4712, "thread_name": "Thread-194 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 39.726845026016235}
{"timestamp": "2025-07-02T10:54:13.922347", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 78.924s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 8364, "thread_name": "Thread-195 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 78.92417931556702}
{"timestamp": "2025-07-02T11:01:49.959373", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 27.413s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 16344, "thread_name": "Thread-15 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 27.412557125091553}
{"timestamp": "2025-07-02T11:02:02.314764", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 10.969s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 7704, "thread_name": "Thread-18 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 10.969314098358154}
{"timestamp": "2025-07-02T11:02:39.055479", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 32.638s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 17432, "thread_name": "Thread-20 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 32.63827300071716}
{"timestamp": "2025-07-02T11:06:29.894941", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 33.197s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18772, "thread_name": "Thread-15 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 33.196824073791504}
{"timestamp": "2025-07-02T11:06:44.868913", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 12.098s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 16588, "thread_name": "Thread-18 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 12.09826946258545}
{"timestamp": "2025-07-02T11:07:20.131562", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 34.731s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 14416, "thread_name": "Thread-20 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 34.731149673461914}
{"timestamp": "2025-07-02T11:15:25.281686", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 28.042s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 9508, "thread_name": "Thread-24 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 28.04150104522705}
{"timestamp": "2025-07-02T11:15:39.824842", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 13.941s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 9312, "thread_name": "Thread-27 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 13.94092321395874}
{"timestamp": "2025-07-02T11:16:15.337536", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 32.657s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 1976, "thread_name": "Thread-29 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 32.65661334991455}
{"timestamp": "2025-07-02T11:26:30.257874", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 28.730s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 7632, "thread_name": "Thread-2 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 28.729541540145874}
{"timestamp": "2025-07-02T11:26:45.678522", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 13.870s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 11424, "thread_name": "Thread-5 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 13.869962692260742}
{"timestamp": "2025-07-02T11:27:24.502526", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 36.870s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 8540, "thread_name": "Thread-7 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 36.870450496673584}
{"timestamp": "2025-07-02T11:32:53.420648", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 26.635s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20004, "thread_name": "Thread-13 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 26.63522982597351}
{"timestamp": "2025-07-02T11:33:08.841725", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 8.696s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 16636, "thread_name": "Thread-19 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 8.695826768875122}
{"timestamp": "2025-07-02T11:33:50.065112", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 39.700s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 1524, "thread_name": "Thread-21 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 39.69964289665222}
{"timestamp": "2025-07-02T11:35:21.348070", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 34.222s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18676, "thread_name": "Thread-23 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 34.22241568565369}
{"timestamp": "2025-07-02T11:35:50.480729", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 14.113s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13120, "thread_name": "Thread-25 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 14.112514019012451}
{"timestamp": "2025-07-02T11:36:57.835772", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 27.459s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 14588, "thread_name": "Thread-27 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 27.459136724472046}
{"timestamp": "2025-07-02T11:37:54.487034", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 19.729s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20432, "thread_name": "Thread-42 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 19.728842973709106}
{"timestamp": "2025-07-02T11:39:09.943671", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 58.339s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 12540, "thread_name": "Thread-45 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 58.33891773223877}
{"timestamp": "2025-07-02T11:41:21.320524", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 18.390s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19648, "thread_name": "Thread-47 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 18.38985562324524}
{"timestamp": "2025-07-02T11:42:04.721205", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 23.415s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 7904, "thread_name": "Thread-49 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 23.415452003479004}
{"timestamp": "2025-07-02T11:43:31.419261", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 29.204s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19360, "thread_name": "Thread-64 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 29.204013109207153}
{"timestamp": "2025-07-02T11:44:54.160461", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 78.190s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 2768, "thread_name": "Thread-67 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 78.18980526924133}
{"timestamp": "2025-07-02T11:45:33.810458", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 28.325s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19752, "thread_name": "Thread-69 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 28.324769020080566}
{"timestamp": "2025-07-02T11:46:02.390917", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 29.251s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 608, "thread_name": "Thread-70 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 29.251309394836426}
{"timestamp": "2025-07-02T11:51:33.331497", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 150.519s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 12320, "thread_name": "Thread-73 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 150.51910305023193}
{"timestamp": "2025-07-02T12:07:58.293290", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 28.454s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20052, "thread_name": "Thread-88 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 28.454050064086914}
{"timestamp": "2025-07-02T12:08:25.990475", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 12.146s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 16376, "thread_name": "Thread-91 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 12.14639139175415}
{"timestamp": "2025-07-02T12:09:05.249434", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 34.728s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 616, "thread_name": "Thread-93 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 34.7281973361969}
{"timestamp": "2025-07-02T12:10:28.659043", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 33.287s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 2124, "thread_name": "Thread-98 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 33.28687500953674}
{"timestamp": "2025-07-02T12:11:14.044187", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 22.519s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19912, "thread_name": "Thread-100 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 22.519498586654663}
{"timestamp": "2025-07-02T12:11:40.225449", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 24.393s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18948, "thread_name": "Thread-102 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 24.392958402633667}
{"timestamp": "2025-07-02T12:12:18.630067", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 35.465s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 2760, "thread_name": "Thread-104 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 35.465113162994385}
{"timestamp": "2025-07-02T12:15:44.930974", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 77.643s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 17488, "thread_name": "Thread-106 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 77.64303493499756}
{"timestamp": "2025-07-02T12:16:02.378597", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 29.696s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 5448, "thread_name": "Thread-107 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 29.695963621139526}
{"timestamp": "2025-07-02T12:16:20.751030", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 14.855s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20080, "thread_name": "Thread-110 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 14.855025291442871}
{"timestamp": "2025-07-02T12:17:06.495179", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 32.870s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13032, "thread_name": "Thread-112 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 32.869739055633545}
{"timestamp": "2025-07-02T12:17:38.254142", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 9.422s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 3528, "thread_name": "Thread-114 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 9.421636819839478}
{"timestamp": "2025-07-02T12:17:50.756882", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 10.024s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 15036, "thread_name": "Thread-116 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 10.024157047271729}
{"timestamp": "2025-07-02T12:18:38.582139", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 12.367s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 14660, "thread_name": "Thread-118 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 12.366940975189209}
{"timestamp": "2025-07-02T12:19:28.302825", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 23.951s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13804, "thread_name": "Thread-133 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 23.951308012008667}
{"timestamp": "2025-07-02T12:20:59.997461", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 71.647s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19700, "thread_name": "Thread-136 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 71.64662742614746}
{"timestamp": "2025-07-02T12:21:57.658862", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 64.851s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 15872, "thread_name": "Thread-137 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 64.85053086280823}
{"timestamp": "2025-07-02T12:22:34.861653", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 41.286s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20244, "thread_name": "Thread-139 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 41.28649878501892}
{"timestamp": "2025-07-02T12:24:04.882683", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 55.079s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 12960, "thread_name": "Thread-142 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 55.07856035232544}
{"timestamp": "2025-07-02T12:24:53.835472", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 50.492s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 16012, "thread_name": "Thread-143 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 50.49191927909851}
{"timestamp": "2025-07-02T12:32:00.004239", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 24.719s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 3948, "thread_name": "Thread-170 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 24.71858501434326}
{"timestamp": "2025-07-02T12:32:12.313087", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 7.770s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 4116, "thread_name": "Thread-173 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 7.770453691482544}
{"timestamp": "2025-07-02T12:32:47.738732", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 34.056s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 17632, "thread_name": "Thread-175 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 34.05560922622681}
{"timestamp": "2025-07-02T12:33:47.788085", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 35.006s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 3624, "thread_name": "Thread-177 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 35.00598216056824}
{"timestamp": "2025-07-02T12:34:24.442240", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 16.795s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 7844, "thread_name": "Thread-179 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 16.79457688331604}
{"timestamp": "2025-07-02T12:34:51.604478", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 25.377s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19976, "thread_name": "Thread-181 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 25.37682867050171}
{"timestamp": "2025-07-02T12:35:27.932489", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 33.329s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 17272, "thread_name": "Thread-183 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 33.32912254333496}
{"timestamp": "2025-07-02T13:37:03.688545", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 30.181s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19232, "thread_name": "Thread-196 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 30.18085527420044}
{"timestamp": "2025-07-02T13:47:02.978547", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 31.682s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 16072, "thread_name": "Thread-15 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 31.68177366256714}
{"timestamp": "2025-07-02T13:47:14.920584", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 9.597s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20452, "thread_name": "Thread-18 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 9.59667181968689}
{"timestamp": "2025-07-02T13:47:57.485535", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 40.164s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13636, "thread_name": "Thread-20 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 40.16448163986206}
{"timestamp": "2025-07-02T14:05:08.277668", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 29.961s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20456, "thread_name": "Thread-15 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 29.960752248764038}
{"timestamp": "2025-07-02T14:09:23.248269", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 30.839s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19452, "thread_name": "Thread-15 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 30.83869481086731}
{"timestamp": "2025-07-02T14:17:34.272927", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 39.523s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19516, "thread_name": "Thread-15 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 39.522621393203735}
{"timestamp": "2025-07-02T14:20:22.931500", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 30.987s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 7604, "thread_name": "Thread-15 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 30.98697566986084}
{"timestamp": "2025-07-02T14:20:42.282168", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 14.110s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19476, "thread_name": "Thread-18 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 14.109554767608643}
{"timestamp": "2025-07-02T14:21:28.070030", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 38.857s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 16892, "thread_name": "Thread-23 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 38.856528520584106}
{"timestamp": "2025-07-02T14:22:26.418715", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 35.054s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19412, "thread_name": "Thread-25 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 35.053874254226685}
{"timestamp": "2025-07-02T14:23:28.223207", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 24.793s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 8708, "thread_name": "Thread-27 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 24.792773246765137}
{"timestamp": "2025-07-02T14:23:59.070515", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 28.686s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 8056, "thread_name": "Thread-29 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 28.686063051223755}
{"timestamp": "2025-07-02T14:24:50.493398", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 32.706s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 3588, "thread_name": "Thread-31 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 32.70578980445862}
{"timestamp": "2025-07-02T14:25:50.236081", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 39.803s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 14824, "thread_name": "Thread-33 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 39.8031849861145}
{"timestamp": "2025-07-02T14:30:04.111496", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 30.320s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 17272, "thread_name": "Thread-27 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 30.32032608985901}
{"timestamp": "2025-07-02T14:30:49.769166", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 38.644s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 11784, "thread_name": "Thread-30 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 38.64425706863403}
{"timestamp": "2025-07-02T14:31:46.821098", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 38.261s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19640, "thread_name": "Thread-32 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 38.26098418235779}
{"timestamp": "2025-07-02T15:11:54.314104", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 33.243s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 10904, "thread_name": "Thread-15 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 33.2434778213501}
{"timestamp": "2025-07-02T15:12:08.025915", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 11.225s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 1876, "thread_name": "Thread-22 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 11.2245032787323}
{"timestamp": "2025-07-02T15:12:55.726680", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 37.997s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 1112, "thread_name": "Thread-24 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 37.997440576553345}
{"timestamp": "2025-07-02T15:13:43.309693", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 34.444s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 12924, "thread_name": "Thread-26 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 34.44395041465759}
{"timestamp": "2025-07-02T15:14:19.688505", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 27.327s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 11632, "thread_name": "Thread-28 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 27.32662296295166}
{"timestamp": "2025-07-02T15:15:01.600914", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 30.749s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 4352, "thread_name": "Thread-30 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 30.749095678329468}
{"timestamp": "2025-07-02T15:15:46.948390", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 36.802s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 15112, "thread_name": "Thread-32 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 36.80225849151611}
{"timestamp": "2025-07-02T15:16:32.989940", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 36.472s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19312, "thread_name": "Thread-34 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 36.47188949584961}
{"timestamp": "2025-07-02T16:05:48.847352", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 72.212s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 7884, "thread_name": "Thread-34 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 72.21214818954468}
{"timestamp": "2025-07-02T16:06:19.929333", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 11.410s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 8316, "thread_name": "Thread-37 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 11.409968614578247}
{"timestamp": "2025-07-02T16:08:24.713314", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 37.135s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18868, "thread_name": "Thread-53 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 37.135101079940796}
{"timestamp": "2025-07-02T16:09:14.714098", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 41.136s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 7996, "thread_name": "Thread-56 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 41.135889291763306}
{"timestamp": "2025-07-02T16:19:50.351646", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 33.028s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 10760, "thread_name": "Thread-19 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 33.02772855758667}
{"timestamp": "2025-07-02T16:22:05.687053", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 91.435s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13396, "thread_name": "Thread-27 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 91.43509197235107}
{"timestamp": "2025-07-02T16:23:21.131349", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 5.564s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 5056, "thread_name": "Thread-29 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 5.563711643218994}
{"timestamp": "2025-07-02T16:24:04.597643", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 37.450s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18828, "thread_name": "Thread-31 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 37.450310468673706}
{"timestamp": "2025-07-02T16:27:10.151263", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 35.852s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13668, "thread_name": "Thread-48 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 35.85182976722717}
{"timestamp": "2025-07-02T17:38:11.343016", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 29.081s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 12872, "thread_name": "Thread-128 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 29.08082151412964}
{"timestamp": "2025-07-02T17:39:59.198873", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 22.855s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 9368, "thread_name": "Thread-132 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 22.854760885238647}
{"timestamp": "2025-07-02T17:40:42.032062", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 40.315s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 11220, "thread_name": "Thread-134 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 40.31494927406311}
{"timestamp": "2025-07-02T17:41:37.147205", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 36.683s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 12960, "thread_name": "Thread-136 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 36.683271408081055}
{"timestamp": "2025-07-02T17:42:26.028671", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 39.582s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 9176, "thread_name": "Thread-139 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 39.58182382583618}
{"timestamp": "2025-07-02T17:43:07.679664", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 33.520s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 1456, "thread_name": "Thread-141 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 33.519901275634766}
{"timestamp": "2025-07-02T17:43:53.991249", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 32.769s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 10332, "thread_name": "Thread-145 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 32.7689790725708}
{"timestamp": "2025-07-02T17:44:54.572453", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 35.354s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 11628, "thread_name": "Thread-148 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 35.35439682006836}
{"timestamp": "2025-07-02T18:00:49.742708", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 28.447s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 1596, "thread_name": "Thread-180 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 28.446721076965332}
{"timestamp": "2025-07-02T18:02:01.892679", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 36.436s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13408, "thread_name": "Thread-197 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 36.43550705909729}
{"timestamp": "2025-07-02T18:02:47.093596", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 8.256s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 10700, "thread_name": "Thread-213 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 8.256241798400879}
{"timestamp": "2025-07-02T18:03:04.770838", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 28.879s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 2020, "thread_name": "Thread-212 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 28.878904581069946}
{"timestamp": "2025-07-02T18:04:03.398994", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 41.431s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 4364, "thread_name": "Thread-221 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 41.43064904212952}
{"timestamp": "2025-07-03T09:31:51.960721", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 29.396s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 16552, "thread_name": "Thread-26 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 29.396143913269043}
{"timestamp": "2025-07-03T09:32:14.545984", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 8.748s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 10184, "thread_name": "Thread-29 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 8.747990608215332}
{"timestamp": "2025-07-03T09:32:59.448380", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 38.833s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 12916, "thread_name": "Thread-31 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 38.83348751068115}
{"timestamp": "2025-07-03T09:33:46.715096", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 34.380s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20220, "thread_name": "Thread-33 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 34.38007640838623}
{"timestamp": "2025-07-03T09:34:22.555005", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 23.011s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18188, "thread_name": "Thread-35 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 23.011192798614502}
{"timestamp": "2025-07-03T09:35:09.410792", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 29.915s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 21492, "thread_name": "Thread-37 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 29.915330410003662}
{"timestamp": "2025-07-03T09:35:59.657307", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 26.939s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 21396, "thread_name": "Thread-39 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 26.93851375579834}
{"timestamp": "2025-07-03T09:37:04.767594", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 35.831s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 16156, "thread_name": "Thread-41 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 35.831127882003784}
{"timestamp": "2025-07-03T14:05:23.355704", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 26.233s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19520, "thread_name": "Thread-27 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 26.233099222183228}
{"timestamp": "2025-07-03T15:16:10.807119", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 28.701s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 4152, "thread_name": "Thread-55 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 28.70050024986267}
{"timestamp": "2025-07-03T15:48:49.707765", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 22.810s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 10456, "thread_name": "Thread-64 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 22.809683799743652}
{"timestamp": "2025-07-03T15:50:30.349635", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 39.379s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19552, "thread_name": "Thread-13 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 39.37880492210388}
{"timestamp": "2025-07-03T15:52:51.565611", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 36.722s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 3444, "thread_name": "Thread-16 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 36.72217154502869}
{"timestamp": "2025-07-03T15:54:25.176575", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 46.216s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 14328, "thread_name": "Thread-49 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 46.21605706214905}
{"timestamp": "2025-07-03T16:31:35.889392", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 31.885s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13888, "thread_name": "Thread-387 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 31.88493776321411}
{"timestamp": "2025-07-03T16:51:20.867187", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 5.524s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 9968, "thread_name": "Thread-529 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 5.523631572723389}
{"timestamp": "2025-07-03T16:56:12.062968", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 4.447s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 11492, "thread_name": "Thread-547 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 4.446753263473511}
{"timestamp": "2025-07-03T16:57:28.197595", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 6.621s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13768, "thread_name": "Thread-16 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 6.62097430229187}
{"timestamp": "2025-07-03T16:57:47.888083", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 4.975s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 12176, "thread_name": "Thread-19 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 4.975325107574463}
{"timestamp": "2025-07-03T16:58:10.816925", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 5.976s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 2604, "thread_name": "Thread-21 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 5.975760459899902}
{"timestamp": "2025-07-03T17:07:28.697558", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 4.359s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 6988, "thread_name": "Thread-101 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 4.3586626052856445}
{"timestamp": "2025-07-03T17:09:15.844514", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 33.068s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 12124, "thread_name": "Thread-118 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 33.06810474395752}
{"timestamp": "2025-07-03T17:10:16.355697", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 41.602s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 14332, "thread_name": "Thread-120 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 41.601590633392334}
{"timestamp": "2025-07-03T17:11:05.521886", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 5.358s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 7556, "thread_name": "Thread-137 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 5.357788801193237}
{"timestamp": "2025-07-03T17:11:25.366092", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 6.780s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13104, "thread_name": "Thread-140 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 6.7800798416137695}
{"timestamp": "2025-07-03T17:14:14.908129", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 5.865s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 12276, "thread_name": "Thread-180 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 5.864986896514893}
{"timestamp": "2025-07-03T17:16:40.414994", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 5.373s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 7460, "thread_name": "Thread-196 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 5.3731536865234375}
{"timestamp": "2025-07-03T17:17:16.832710", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 28.032s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 12048, "thread_name": "Thread-211 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 28.031503438949585}
{"timestamp": "2025-07-03T17:18:10.619339", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 41.821s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 8856, "thread_name": "Thread-214 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 41.82130408287048}
{"timestamp": "2025-07-03T17:18:52.331859", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 12.457s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19148, "thread_name": "Thread-217 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 12.45676875114441}
{"timestamp": "2025-07-03T17:19:38.820888", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 27.614s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 12576, "thread_name": "Thread-233 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 27.614012956619263}
{"timestamp": "2025-07-03T17:22:13.395465", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 8.565s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 7012, "thread_name": "Thread-273 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 8.564976215362549}
{"timestamp": "2025-07-03T17:22:36.563427", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 18.166s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 17644, "thread_name": "Thread-276 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 18.165993213653564}
{"timestamp": "2025-07-03T17:23:52.345707", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 41.312s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 14724, "thread_name": "Thread-292 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 41.31154990196228}
{"timestamp": "2025-07-03T17:44:08.543936", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 4.875s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 1700, "thread_name": "Thread-127 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 4.874637126922607}
{"timestamp": "2025-07-04T08:46:15.437336", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 5.255s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 5396, "thread_name": "Thread-17 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 5.255177021026611}
{"timestamp": "2025-07-04T08:49:09.810122", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 2.954s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 7540, "thread_name": "Thread-64 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 2.95414137840271}
{"timestamp": "2025-07-04T08:49:37.598464", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 14.712s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 16588, "thread_name": "Thread-67 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 14.711961507797241}
{"timestamp": "2025-07-04T08:49:52.311939", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 12.373s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13816, "thread_name": "Thread-72 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 12.37289810180664}
{"timestamp": "2025-07-04T08:50:05.049911", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 4.801s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13712, "thread_name": "Thread-93 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 4.801042318344116}
{"timestamp": "2025-07-04T08:50:35.265772", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 11.933s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 2476, "thread_name": "Thread-115 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 11.933403253555298}
{"timestamp": "2025-07-04T08:54:54.756429", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 29.747s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 10836, "thread_name": "Thread-185 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 29.747487545013428}
{"timestamp": "2025-07-04T08:55:48.577970", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 41.168s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 4820, "thread_name": "Thread-200 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 41.168269872665405}
{"timestamp": "2025-07-04T08:59:27.549993", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 5.433s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 2788, "thread_name": "Thread-269 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 5.433111667633057}
{"timestamp": "2025-07-04T08:59:33.388737", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 5.129s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 8300, "thread_name": "Thread-271 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 5.128707408905029}
{"timestamp": "2025-07-04T08:59:39.215261", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 5.930s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18604, "thread_name": "Thread-284 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 5.929560661315918}
{"timestamp": "2025-07-04T08:59:45.286481", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 5.777s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 3908, "thread_name": "Thread-286 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 5.777114152908325}
{"timestamp": "2025-07-04T08:59:54.393159", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 6.719s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 6816, "thread_name": "Thread-289 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 6.718730926513672}
{"timestamp": "2025-07-04T09:08:06.119225", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 28.765s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13164, "thread_name": "Thread-545 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 28.764827728271484}
{"timestamp": "2025-07-04T09:08:23.130352", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 28.892s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 14768, "thread_name": "Thread-588 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 28.89215922355652}
{"timestamp": "2025-07-04T09:10:51.415718", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 37.659s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 11832, "thread_name": "Thread-605 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 37.65878391265869}
{"timestamp": "2025-07-04T09:10:54.958037", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 36.328s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19404, "thread_name": "Thread-606 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 36.32835936546326}
{"timestamp": "2025-07-04T09:15:23.886166", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 28.310s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 12216, "thread_name": "Thread-642 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 28.309961795806885}
{"timestamp": "2025-07-04T09:17:17.590326", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 15.256s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13020, "thread_name": "Thread-765 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 15.255516290664673}
{"timestamp": "2025-07-04T09:18:04.350569", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 7.104s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 3608, "thread_name": "Thread-768 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 7.103858709335327}
{"timestamp": "2025-07-04T09:28:17.837275", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 27.397s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19964, "thread_name": "Thread-918 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 27.39725685119629}
{"timestamp": "2025-07-04T09:29:25.059163", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 41.246s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 12728, "thread_name": "Thread-920 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 41.246076583862305}
{"timestamp": "2025-07-04T09:30:21.144945", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 4.120s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 7876, "thread_name": "Thread-996 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 4.119666337966919}
{"timestamp": "2025-07-04T09:32:33.097086", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 81.056s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 12444, "thread_name": "Thread-1015 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 81.05627846717834}
{"timestamp": "2025-07-04T09:38:29.933263", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 26.722s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 17072, "thread_name": "Thread-1147 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 26.722126960754395}
{"timestamp": "2025-07-04T09:39:22.851602", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 38.585s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 3376, "thread_name": "Thread-1151 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 38.58514952659607}
{"timestamp": "2025-07-04T09:50:35.453957", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 7.909s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19688, "thread_name": "Thread-1234 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 7.908758878707886}
{"timestamp": "2025-07-04T10:05:05.673217", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 18.636s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18408, "thread_name": "Thread-16 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 18.63607668876648}
{"timestamp": "2025-07-04T10:30:52.406256", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 38.756s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 9224, "thread_name": "Thread-384 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 38.75642919540405}
{"timestamp": "2025-07-04T10:31:40.747020", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 37.948s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 8988, "thread_name": "Thread-387 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 37.94784331321716}
{"timestamp": "2025-07-04T10:31:50.589382", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 7.587s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 14920, "thread_name": "Thread-389 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 7.587049722671509}
{"timestamp": "2025-07-04T10:32:03.558103", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 10.394s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 8404, "thread_name": "Thread-391 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 10.393638849258423}
{"timestamp": "2025-07-04T10:41:45.205910", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 6.379s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 10700, "thread_name": "Thread-518 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 6.378689527511597}
{"timestamp": "2025-07-04T10:42:35.371453", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 4.809s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 16492, "thread_name": "Thread-579 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 4.809478282928467}
{"timestamp": "2025-07-04T10:43:20.074440", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 6.807s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 10652, "thread_name": "Thread-594 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 6.80735445022583}
{"timestamp": "2025-07-04T10:44:44.310134", "level": "INFO", "logger": "api", "message": "API调用 - POST http://10.2.18.118:8000/chat/stream (状态码: 200) (耗时: 5.632s)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 5596, "thread_name": "Thread-623 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": 5.632208824157715}
{"timestamp": "2025-07-07T16:56:27.880461", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 16684, "thread_name": "Thread-13 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-07T16:56:49.098856", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13888, "thread_name": "Thread-11 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-07T17:16:18.650389", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19132, "thread_name": "Thread-9 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-07T17:19:54.085940", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 14012, "thread_name": "Thread-58 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-07T17:20:40.710297", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 17268, "thread_name": "Thread-73 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-07T17:33:04.660061", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 21156, "thread_name": "Thread-11 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-07T17:33:56.353281", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 8848, "thread_name": "Thread-26 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-07T17:35:05.281668", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 700, "thread_name": "Thread-17 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-07T17:36:00.150701", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 8856, "thread_name": "Thread-43 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-07T17:37:15.101311", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 14484, "thread_name": "Thread-11 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-07T17:37:57.228884", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13992, "thread_name": "Thread-12 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-07T17:38:52.710860", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 3300, "thread_name": "Thread-38 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-07T17:40:50.675428", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13476, "thread_name": "Thread-53 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T08:30:00.959447", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 11688, "thread_name": "Thread-11 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T08:38:14.132568", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13308, "thread_name": "Thread-15 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T08:42:08.470382", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 11320, "thread_name": "Thread-13 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T08:44:01.332023", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13808, "thread_name": "Thread-28 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T08:44:22.892888", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 21248, "thread_name": "Thread-44 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T08:44:28.656412", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13140, "thread_name": "Thread-59 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T08:48:28.518666", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 15296, "thread_name": "Thread-11 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T08:50:55.540362", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 7976, "thread_name": "Thread-9 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T08:57:15.969768", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 17536, "thread_name": "Thread-11 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T09:05:57.735052", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 9180, "thread_name": "Thread-19 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T09:08:30.532400", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 8636, "thread_name": "Thread-39 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T09:10:17.032381", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 21124, "thread_name": "Thread-61 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T09:15:16.609496", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 17548, "thread_name": "Thread-9 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T09:15:38.391451", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 15796, "thread_name": "Thread-24 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T09:18:19.044951", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 1704, "thread_name": "Thread-39 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T09:18:52.050527", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 8136, "thread_name": "Thread-9 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T09:21:20.777958", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18804, "thread_name": "Thread-24 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T09:21:30.060552", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20180, "thread_name": "Thread-37 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T09:23:26.044852", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 7664, "thread_name": "Thread-64 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T09:24:06.551734", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 6752, "thread_name": "Thread-10 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T09:24:41.215109", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 16568, "thread_name": "Thread-24 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T09:25:07.773862", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18744, "thread_name": "Thread-36 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T09:27:11.911993", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 17920, "thread_name": "Thread-9 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T09:27:43.169014", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 16720, "thread_name": "Thread-24 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T09:27:55.464299", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 12140, "thread_name": "Thread-38 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T09:28:20.958539", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 4712, "thread_name": "Thread-51 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T09:29:46.980887", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13220, "thread_name": "Thread-67 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T09:30:44.082343", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 12844, "thread_name": "Thread-84 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T09:31:32.653155", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19232, "thread_name": "Thread-114 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T09:32:32.406324", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 8444, "thread_name": "Thread-9 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T09:32:42.885524", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 17928, "thread_name": "Thread-22 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T09:33:01.771759", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 17672, "thread_name": "Thread-35 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T09:34:38.919508", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13996, "thread_name": "Thread-52 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T09:35:13.451993", "level": "INFO", "logger": "api", "message": "API调用 - POST /api/security/check (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 14984, "thread_name": "Thread-56 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T09:36:15.101327", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 8964, "thread_name": "Thread-66 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T09:39:19.552361", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 16036, "thread_name": "Thread-9 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T09:39:34.475626", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20560, "thread_name": "Thread-31 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T09:39:40.728447", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 4216, "thread_name": "Thread-45 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T09:46:54.909795", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 14620, "thread_name": "Thread-11 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:03:34.112468", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 15788, "thread_name": "Thread-203 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:04:27.064758", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19936, "thread_name": "Thread-218 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:04:46.647469", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13552, "thread_name": "Thread-243 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:05:14.114264", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 14876, "thread_name": "Thread-255 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:07:39.902221", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19860, "thread_name": "Thread-283 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:07:51.891191", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13836, "thread_name": "Thread-298 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:08:09.115609", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18128, "thread_name": "Thread-312 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:09:52.998860", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13756, "thread_name": "Thread-327 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:10:42.947688", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 15676, "thread_name": "Thread-341 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:10:54.788335", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 12108, "thread_name": "Thread-353 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:11:12.940862", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18896, "thread_name": "Thread-366 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:11:21.749142", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18352, "thread_name": "Thread-379 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:12:00.062725", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 14204, "thread_name": "Thread-392 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:12:40.516497", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 9808, "thread_name": "Thread-408 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:13:02.805421", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 7044, "thread_name": "Thread-428 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:13:49.007810", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 12704, "thread_name": "Thread-440 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:14:01.438232", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 6840, "thread_name": "Thread-456 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:14:45.083613", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19936, "thread_name": "Thread-474 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:16:25.882087", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 6520, "thread_name": "Thread-489 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:20:44.113864", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19176, "thread_name": "Thread-12 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:21:33.492106", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20268, "thread_name": "Thread-27 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:22:03.467828", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 14116, "thread_name": "Thread-40 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:22:42.853077", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 5012, "thread_name": "Thread-10 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:23:25.467064", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 21016, "thread_name": "Thread-24 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:26:24.127235", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18740, "thread_name": "Thread-38 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:26:28.944040", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13600, "thread_name": "Thread-52 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:26:34.550324", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 15240, "thread_name": "Thread-63 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:27:53.268699", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19632, "thread_name": "Thread-77 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:28:48.870911", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20496, "thread_name": "Thread-89 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:29:05.685055", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 15152, "thread_name": "Thread-102 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:30:43.210062", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20004, "thread_name": "Thread-125 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:32:09.984540", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 10040, "thread_name": "Thread-12 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:32:14.029495", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20304, "thread_name": "Thread-28 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:37:08.961962", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13404, "thread_name": "Thread-11 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:37:22.084812", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 10436, "thread_name": "Thread-28 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:37:44.200584", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 12352, "thread_name": "Thread-43 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:40:02.602130", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 8000, "thread_name": "Thread-62 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:42:09.248525", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19568, "thread_name": "Thread-77 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:42:50.527251", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 4800, "thread_name": "Thread-89 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:46:49.511504", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18168, "thread_name": "Thread-111 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:47:10.152974", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18312, "thread_name": "Thread-124 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:49:05.518687", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19812, "thread_name": "Thread-139 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:50:50.962277", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 3684, "thread_name": "Thread-9 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:57:05.324359", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 6768, "thread_name": "Thread-35 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T10:59:30.436990", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20928, "thread_name": "Thread-5 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:00:26.021697", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 7216, "thread_name": "Thread-4 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:00:29.975221", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19740, "thread_name": "Thread-8 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:00:54.551297", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 9648, "thread_name": "Thread-12 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:02:46.622464", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 17080, "thread_name": "Thread-17 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:03:05.502451", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 8076, "thread_name": "Thread-29 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:04:13.501110", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 5228, "thread_name": "Thread-39 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:04:24.639398", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19236, "thread_name": "Thread-43 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:04:37.613617", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 15544, "thread_name": "Thread-55 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:09:13.223518", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 14192, "thread_name": "Thread-88 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:09:19.661489", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13860, "thread_name": "Thread-100 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:11:35.771564", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20004, "thread_name": "Thread-119 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:12:40.228854", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 16128, "thread_name": "Thread-133 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:14:15.920074", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 17692, "thread_name": "Thread-157 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:14:52.399962", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 11284, "thread_name": "Thread-175 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:15:39.706722", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 12668, "thread_name": "Thread-199 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:16:49.270869", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 8224, "thread_name": "Thread-9 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:19:53.737049", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 21476, "thread_name": "Thread-9 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:19:58.019607", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20188, "thread_name": "Thread-31 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:20:39.016880", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 15340, "thread_name": "Thread-47 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:23:18.702851", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18612, "thread_name": "Thread-61 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:24:12.055397", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 15076, "thread_name": "Thread-74 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:24:31.309357", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19948, "thread_name": "Thread-94 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:24:41.408436", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19356, "thread_name": "Thread-111 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:24:53.273880", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 8152, "thread_name": "Thread-125 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:25:14.705680", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 6572, "thread_name": "Thread-137 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:25:48.161513", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20588, "thread_name": "Thread-154 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:27:07.960728", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 1484, "thread_name": "Thread-170 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:28:05.952803", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 21024, "thread_name": "Thread-10 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:30:35.312780", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18904, "thread_name": "Thread-28 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:31:01.163215", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 7668, "thread_name": "Thread-46 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:31:29.918054", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 1520, "thread_name": "Thread-61 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:32:12.376076", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 16272, "thread_name": "Thread-77 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:33:19.022290", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 14192, "thread_name": "Thread-93 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:33:58.472293", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 8588, "thread_name": "Thread-108 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:34:13.878260", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 6456, "thread_name": "Thread-126 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:35:24.764660", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 21244, "thread_name": "Thread-142 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:35:41.459887", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 11800, "thread_name": "Thread-154 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:37:46.603906", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18840, "thread_name": "Thread-167 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T11:37:56.855881", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19624, "thread_name": "Thread-181 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T12:00:20.955136", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13360, "thread_name": "Thread-199 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T12:00:31.447393", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 21000, "thread_name": "Thread-213 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T12:02:33.746374", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20872, "thread_name": "Thread-227 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T12:02:54.087025", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19568, "thread_name": "Thread-240 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T12:03:54.911389", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18996, "thread_name": "Thread-253 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T12:05:06.834873", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 8604, "thread_name": "Thread-265 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T12:05:42.381803", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20876, "thread_name": "Thread-278 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T12:05:54.308841", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20984, "thread_name": "Thread-291 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T12:07:25.256216", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 9120, "thread_name": "Thread-305 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T12:11:39.766211", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 5436, "thread_name": "Thread-327 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T12:12:42.441639", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 3996, "thread_name": "Thread-342 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T12:12:50.008548", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 21384, "thread_name": "Thread-353 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T12:12:57.231990", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 1416, "thread_name": "Thread-366 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T12:13:14.920757", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18492, "thread_name": "Thread-381 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T12:13:56.074822", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 4432, "thread_name": "Thread-393 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T12:14:14.614726", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 21380, "thread_name": "Thread-408 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T12:15:29.662907", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19356, "thread_name": "Thread-421 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T12:15:36.382212", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18276, "thread_name": "Thread-436 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T12:15:41.661638", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 8588, "thread_name": "Thread-447 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T12:16:45.307370", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 11748, "thread_name": "Thread-460 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T12:17:43.396350", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 8064, "thread_name": "Thread-474 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T12:17:57.518096", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 1720, "thread_name": "Thread-487 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T12:18:21.929829", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 3344, "thread_name": "Thread-500 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T12:18:24.662514", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 16240, "thread_name": "Thread-512 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T12:18:52.124026", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 11388, "thread_name": "Thread-525 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T12:19:40.982619", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 4188, "thread_name": "Thread-539 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T12:19:47.913141", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 1724, "thread_name": "Thread-553 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T12:20:29.741653", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20304, "thread_name": "Thread-577 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T12:20:53.766705", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 12352, "thread_name": "Thread-592 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T12:21:20.265770", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13964, "thread_name": "Thread-604 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T12:21:43.420963", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 8144, "thread_name": "Thread-619 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T12:24:28.381011", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13332, "thread_name": "Thread-632 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T12:24:50.480489", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 3312, "thread_name": "Thread-657 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T12:38:53.104205", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20568, "thread_name": "Thread-12 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T12:42:23.311425", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 16108, "thread_name": "Thread-38 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T12:44:46.304766", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19208, "thread_name": "Thread-66 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T13:37:32.529333", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 2828, "thread_name": "Thread-106 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T13:37:39.653072", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 6888, "thread_name": "Thread-126 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T13:42:56.869180", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 16108, "thread_name": "Thread-14 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T13:43:19.756535", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 17368, "thread_name": "Thread-35 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T13:43:37.925670", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18740, "thread_name": "Thread-56 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T13:44:08.136778", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 9348, "thread_name": "Thread-69 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T13:46:59.006765", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 14172, "thread_name": "Thread-126 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T13:47:16.475247", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18904, "thread_name": "Thread-137 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T13:47:26.234259", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 616, "thread_name": "Thread-150 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T13:47:34.246052", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 12296, "thread_name": "Thread-163 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T13:48:18.441807", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18284, "thread_name": "Thread-176 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T13:51:39.200699", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 7744, "thread_name": "Thread-202 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T13:52:44.645588", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 17672, "thread_name": "Thread-218 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T13:52:54.322816", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 7732, "thread_name": "Thread-238 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T13:57:00.866595", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 4832, "thread_name": "Thread-252 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T13:57:15.740629", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 7960, "thread_name": "Thread-266 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T13:57:56.444247", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 7100, "thread_name": "Thread-278 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T13:58:08.215684", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 15924, "thread_name": "Thread-292 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T13:58:46.233353", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19064, "thread_name": "Thread-319 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T13:58:52.191463", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 1520, "thread_name": "Thread-332 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T13:59:04.802956", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 21096, "thread_name": "Thread-345 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T13:59:10.804699", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18980, "thread_name": "Thread-358 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T13:59:25.044408", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 7100, "thread_name": "Thread-375 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T13:59:33.452326", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 8572, "thread_name": "Thread-391 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T13:59:39.594914", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 15864, "thread_name": "Thread-407 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T14:40:17.336182", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13760, "thread_name": "Thread-22 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T14:41:48.702491", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 6660, "thread_name": "Thread-42 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T14:42:01.306362", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 1840, "thread_name": "Thread-57 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T14:42:26.629499", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 21116, "thread_name": "Thread-72 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T14:42:36.242099", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 16132, "thread_name": "Thread-87 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T14:42:48.325120", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20268, "thread_name": "Thread-102 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T14:42:55.368279", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 3172, "thread_name": "Thread-118 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T14:43:01.284989", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 9408, "thread_name": "Thread-130 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T14:43:21.644494", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18844, "thread_name": "Thread-143 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T14:44:02.623501", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 3212, "thread_name": "Thread-160 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T14:44:15.702977", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 8028, "thread_name": "Thread-170 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T14:44:35.790760", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 5492, "thread_name": "Thread-188 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T14:44:46.633610", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 21460, "thread_name": "Thread-201 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T14:45:06.402128", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 6576, "thread_name": "Thread-221 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T14:45:19.345950", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20192, "thread_name": "Thread-235 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T14:45:39.470482", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18404, "thread_name": "Thread-249 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T14:46:18.530927", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 2192, "thread_name": "Thread-265 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T14:46:41.074045", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 21408, "thread_name": "Thread-281 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T14:48:08.313664", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 16440, "thread_name": "Thread-297 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T14:48:36.425423", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 15116, "thread_name": "Thread-314 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T14:50:55.906269", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 6320, "thread_name": "Thread-328 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T14:51:10.895165", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 21088, "thread_name": "Thread-349 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T14:51:20.144975", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 11456, "thread_name": "Thread-363 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T14:51:31.531294", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 12028, "thread_name": "Thread-379 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T14:51:54.085116", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 6284, "thread_name": "Thread-400 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T14:53:19.425996", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 5348, "thread_name": "Thread-418 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T14:54:02.862557", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 14620, "thread_name": "Thread-459 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T14:54:50.932024", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 17900, "thread_name": "Thread-475 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T14:55:34.708263", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 15116, "thread_name": "Thread-498 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T14:56:19.038318", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20072, "thread_name": "Thread-511 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T14:58:39.463611", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 5012, "thread_name": "Thread-528 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T14:59:24.030087", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 880, "thread_name": "Thread-547 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T14:59:39.353948", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13860, "thread_name": "Thread-559 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T15:01:34.586844", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13292, "thread_name": "Thread-574 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T15:01:42.883301", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 6288, "thread_name": "Thread-589 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T15:02:55.252343", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 15624, "thread_name": "Thread-603 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T15:05:27.958213", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 7552, "thread_name": "Thread-622 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T15:05:35.703841", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20128, "thread_name": "Thread-637 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T15:05:44.205230", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 17900, "thread_name": "Thread-650 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T15:08:18.320998", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 3180, "thread_name": "Thread-10 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T15:09:42.116747", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 3768, "thread_name": "Thread-26 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T15:14:49.845036", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 9148, "thread_name": "Thread-58 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T15:17:28.163357", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19160, "thread_name": "Thread-78 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T15:25:11.386932", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 2836, "thread_name": "Thread-12 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T15:28:26.878371", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 16508, "thread_name": "Thread-26 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T15:31:02.040952", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19656, "thread_name": "Thread-50 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T15:34:04.961182", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 17580, "thread_name": "Thread-66 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T16:04:12.642629", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 11392, "thread_name": "Thread-12 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T16:05:55.169150", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 9104, "thread_name": "Thread-41 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T16:08:31.104926", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 5184, "thread_name": "Thread-65 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T16:38:01.931489", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 17172, "thread_name": "Thread-165 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T16:52:06.404260", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18072, "thread_name": "Thread-344 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T16:55:54.308265", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 10284, "thread_name": "Thread-378 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T16:56:52.865551", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 16116, "thread_name": "Thread-12 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T17:03:18.846407", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 12568, "thread_name": "Thread-51 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T17:03:32.700636", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 3888, "thread_name": "Thread-64 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T17:04:08.985166", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 14300, "thread_name": "Thread-78 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T17:09:15.495606", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13920, "thread_name": "Thread-55 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T17:13:28.970426", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 12568, "thread_name": "Thread-73 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T17:21:27.104852", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 4972, "thread_name": "Thread-94 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T17:27:46.837592", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 8348, "thread_name": "Thread-122 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T17:38:27.205973", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 4588, "thread_name": "Thread-165 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T17:46:17.264246", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13508, "thread_name": "Thread-10 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T17:47:19.222721", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 12600, "thread_name": "Thread-32 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T17:48:59.034332", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 9728, "thread_name": "Thread-54 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T17:50:10.808933", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13664, "thread_name": "Thread-81 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T17:53:16.374906", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 2768, "thread_name": "Thread-101 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-08T17:53:55.859240", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 2936, "thread_name": "Thread-117 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-09T08:49:47.958575", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 4868, "thread_name": "Thread-8 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T15:14:30.716991", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19780, "thread_name": "Thread-12 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T15:14:34.370474", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 11504, "thread_name": "Thread-23 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T15:19:35.627364", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19600, "thread_name": "Thread-73 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T15:19:56.772129", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18416, "thread_name": "Thread-90 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T15:21:07.037115", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 11372, "thread_name": "Thread-105 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T15:21:57.051758", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 896, "thread_name": "Thread-123 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T15:24:00.454356", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 32, "thread_name": "Thread-8 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T15:27:26.823031", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 9412, "thread_name": "Thread-30 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T15:28:03.194794", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 15348, "thread_name": "Thread-52 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T15:32:06.838166", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 4688, "thread_name": "Thread-89 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T15:32:11.362615", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13812, "thread_name": "Thread-102 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T15:32:27.708153", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 15388, "thread_name": "Thread-116 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T15:33:35.818766", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 10560, "thread_name": "Thread-147 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T15:33:55.325077", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 15388, "thread_name": "Thread-160 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T16:50:31.812062", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18040, "thread_name": "Thread-14 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T16:51:00.277311", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20628, "thread_name": "Thread-10 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T16:52:54.843513", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 3412, "thread_name": "Thread-46 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T16:54:08.820246", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 21224, "thread_name": "Thread-71 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T16:55:43.541925", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 6764, "thread_name": "Thread-94 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T16:55:57.020421", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 21492, "thread_name": "Thread-108 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T17:02:14.945735", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 11380, "thread_name": "Thread-143 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T17:02:35.851460", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 16488, "thread_name": "Thread-160 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T17:03:11.380269", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 21240, "thread_name": "Thread-175 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T17:07:58.066788", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 7860, "thread_name": "Thread-189 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T17:08:22.785611", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18412, "thread_name": "Thread-209 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T17:09:19.177612", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 8136, "thread_name": "Thread-223 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T17:09:27.662609", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20752, "thread_name": "Thread-237 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T17:13:14.160847", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18480, "thread_name": "Thread-251 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T17:13:35.927034", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 16272, "thread_name": "Thread-262 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T17:13:41.842541", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 17884, "thread_name": "Thread-282 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T17:17:56.822872", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 17740, "thread_name": "Thread-297 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T17:22:29.825283", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19968, "thread_name": "Thread-313 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T17:29:34.738530", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 4912, "thread_name": "Thread-328 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T17:34:32.106060", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 21380, "thread_name": "Thread-343 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T17:38:27.868661", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19196, "thread_name": "Thread-353 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T17:41:45.967402", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 6348, "thread_name": "Thread-366 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T17:48:46.773969", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 2016, "thread_name": "Thread-383 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-10T17:50:42.545414", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20484, "thread_name": "Thread-399 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-11T09:07:51.020326", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "_log_worker", "line": 71, "thread": 14548, "thread_name": "Thread-1 (_log_worker)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-11T09:07:55.043897", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "_log_worker", "line": 71, "thread": 14548, "thread_name": "Thread-1 (_log_worker)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-11T09:08:02.739580", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "_log_worker", "line": 71, "thread": 14548, "thread_name": "Thread-1 (_log_worker)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-11T09:08:42.839172", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "_log_worker", "line": 71, "thread": 14548, "thread_name": "Thread-1 (_log_worker)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-11T10:14:55.030389", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13664, "thread_name": "Thread-10 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-11T10:21:02.742873", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 268, "thread_name": "Thread-30 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-11T10:28:26.948717", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 9944, "thread_name": "Thread-50 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-11T10:38:45.373207", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 10288, "thread_name": "Thread-9 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-11T10:44:29.738202", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 3152, "thread_name": "Thread-38 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-11T10:46:58.116527", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 12332, "thread_name": "Thread-10 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-11T10:53:03.021755", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 11596, "thread_name": "Thread-44 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-11T10:57:31.988805", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13884, "thread_name": "Thread-100 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-11T11:10:26.660313", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 12780, "thread_name": "Thread-112 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-11T11:12:50.393853", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20268, "thread_name": "Thread-130 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-11T11:14:02.700467", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 1924, "thread_name": "Thread-139 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-11T12:15:01.156469", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18616, "thread_name": "Thread-8 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-11T12:26:14.849962", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 10836, "thread_name": "waitress-3", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-11T12:26:22.489069", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18200, "thread_name": "waitress-2", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-11T12:34:59.702147", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20932, "thread_name": "waitress-1", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-11T12:47:35.982173", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 4120, "thread_name": "waitress-2", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-11T13:50:04.404868", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 12568, "thread_name": "waitress-3", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-11T14:07:50.213396", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 6916, "thread_name": "Thread-12 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-11T14:08:18.536010", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20736, "thread_name": "Thread-23 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-11T14:10:39.202104", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 19536, "thread_name": "Thread-40 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-11T14:10:47.591389", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18912, "thread_name": "Thread-51 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-11T14:20:39.329033", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 6916, "thread_name": "Thread-11 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-11T14:25:23.591108", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 8036, "thread_name": "Thread-8 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-11T14:30:08.645876", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 20256, "thread_name": "Thread-11 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-11T14:31:02.971671", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 13920, "thread_name": "Thread-29 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-11T14:31:58.696687", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 11460, "thread_name": "Thread-44 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-11T14:32:38.331940", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 9220, "thread_name": "Thread-63 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-11T14:33:58.539066", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 3512, "thread_name": "Thread-10 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-11T14:35:42.933638", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 18304, "thread_name": "Thread-28 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-11T14:38:49.187450", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 2084, "thread_name": "Thread-65 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-11T14:42:51.329376", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 3004, "thread_name": "Thread-94 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
{"timestamp": "2025-07-11T14:43:05.544386", "level": "INFO", "logger": "api", "message": "API调用 - GET /api/security/config (状态码: 200)", "module": "logger_config", "function": "log_api_call", "line": 196, "thread": 10360, "thread_name": "Thread-108 (process_request_thread)", "user_id": null, "operation": "api_call", "duration": null}
